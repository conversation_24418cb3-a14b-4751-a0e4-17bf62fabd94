'use client'

import { useEffect, useState } from 'react'

interface ToastProps {
  message: string
  type?: 'success' | 'error' | 'info'
  duration?: number
  onClose?: () => void
}

export default function Toast({ message, type = 'info', duration = 5000, onClose }: ToastProps) {
  const [visible, setVisible] = useState(true)

  useEffect(() => {
    const timer = setTimeout(() => {
      setVisible(false)
      if (onClose) onClose()
    }, duration)

    return () => clearTimeout(timer)
  }, [duration, onClose])

  if (!visible) return null

  let bgColor = 'bg-blue-600'
  let textColor = 'text-white'

  if (type === 'success') {
    bgColor = 'bg-green-600'
  } else if (type === 'error') {
    bgColor = 'bg-red-600'
  }

  return (
    <div
      className={`fixed top-5 left-1/2 transform -translate-x-1/2 z-50 px-6 py-3 rounded-md shadow-lg flex items-center space-x-4 ${bgColor} ${textColor}`}
      role="alert"
    >
      <span className="flex-1">{message}</span>
      <button
        onClick={() => {
          setVisible(false)
          if (onClose) onClose()
        }}
        aria-label="Close notification"
        className="text-white hover:text-gray-200 focus:outline-none"
      >
        &#x2715;
      </button>
    </div>
  )
}

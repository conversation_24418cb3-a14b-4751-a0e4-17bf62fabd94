'use client'

import { useState } from 'react'

interface TooltipProps {
  id: string
  content: string
  children: React.ReactNode
}

export function Tooltip({ id, content, children }: TooltipProps) {
  const [show, setShow] = useState(false)

  return (
    <div 
      className="relative inline-block"
      onMouseEnter={() => setShow(true)}
      onMouseLeave={() => setShow(false)}
    >
      {children}
      {show && (
        <div
          id={id}
          role="tooltip"
          className="absolute z-10 px-3 py-2 text-sm font-medium text-white bg-gray-900 rounded-lg shadow-sm tooltip"
        >
          {content}
          <div className="tooltip-arrow" data-popper-arrow></div>
        </div>
      )}
    </div>
  )
}

'use client'

import { useEffect, useState, Suspense } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import AuthForm from './AuthForm'
import AuthHeader from './AuthHeader'
import Link from 'next/link'
import Image from 'next/image'
import Toast from '../ui/toast'

function AuthContent() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [view, setView] = useState('sign-in')
  const [mounted, setMounted] = useState(false)
  const [toastMessage, setToastMessage] = useState<string | null>(null)
  const supabase = createClientComponentClient()

  useEffect(() => {
    setMounted(true)
    const viewParam = searchParams.get('view')
    if (viewParam) {
      setView(viewParam)
    }

    const messageParam = searchParams.get('message')
    if (messageParam) {
      setToastMessage(decodeURIComponent(messageParam))
    }

    const checkUser = async () => {
      const { data: { session } } = await supabase.auth.getSession()
      if (session) {
        router.push('/dashboard')
      }
    }

    checkUser()
  }, [router, searchParams, supabase.auth])

  if (!mounted) {
    return (
      <div className="min-h-screen bg-[#0A0A0A] flex items-center justify-center">
        <div className="text-white">Loading...</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-[#0A0A0A]">
      {/* Header */}
      <AuthHeader />

      {/* Toast Notification */}
      {toastMessage && (
        <Toast
          message={toastMessage}
          type="success"
          onClose={() => setToastMessage(null)}
          duration={5000}
        />
      )}

      {/* Main Content */}
      <div className="min-h-screen flex flex-col pt-20">
        <AuthForm view={view} />
      </div>
    </div>
  )
}

export default function AuthPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-[#0A0A0A] flex items-center justify-center">
        <div className="text-white">Loading...</div>
      </div>
    }>
      <AuthContent />
    </Suspense>
  )
}

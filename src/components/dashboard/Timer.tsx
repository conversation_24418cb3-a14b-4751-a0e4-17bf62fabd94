"use client";

import React, { useState, useEffect } from 'react';

interface TimerProps {
  initialMinutes: number;
}

const Timer: React.FC<TimerProps> = ({ initialMinutes }) => {
  const [timeRemaining, setTimeRemaining] = useState(initialMinutes * 60); // time in seconds
  const [isActive, setIsActive] = useState(false);

  // Update timeRemaining when initialMinutes prop changes
  useEffect(() => {
    setTimeRemaining(initialMinutes * 60);
    setIsActive(false); // Reset timer when duration changes
  }, [initialMinutes]);

  useEffect(() => {
    let interval: NodeJS.Timeout | null = null;
    if (isActive && timeRemaining > 0) {
      interval = setInterval(() => {
        setTimeRemaining((prevTime) => prevTime - 1);
      }, 1000);
    } else if (!isActive && timeRemaining !== 0 && interval) {
      clearInterval(interval);
    } else if (timeRemaining === 0) {
      // Handle timer end (e.g., show a message, disable inputs)
      console.log("Timer ended!");
      if (interval) clearInterval(interval);
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isActive, timeRemaining]);

  const minutes = Math.floor(timeRemaining / 60);
  const seconds = timeRemaining % 60;

  const displayTime = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')} mins`; // Added "mins"

  const handleStartPause = () => {
    setIsActive(!isActive);
  };

  const handleReset = () => {
    setIsActive(false);
    setTimeRemaining(initialMinutes * 60);
  };

  return (
    <div style={{
      display: 'flex',
      alignItems: 'center',
      gap: '10px',
      fontSize: '1.5em',
      fontWeight: 'bold',
      color: timeRemaining <= 60 ? '#e53935' : '#222', // Red when less than 60 seconds
      background: '#f5f7fa',
      borderRadius: 8,
      padding: '8px 12px',
      boxShadow: '0 2px 8px 0 rgba(60,72,88,0.07)',
      minWidth: 180, // Increased minWidth to accommodate "mins"
      justifyContent: 'center'
    }}>
      <span style={{ fontSize: '0.8em' }}>{displayTime}</span> {/* Further reduced font size */}
      <button onClick={handleStartPause} style={{
        padding: '3px 6px', // Further reduced padding
        fontSize: '0.7em', // Further reduced font size for button text
        border: '1px solid #ccc', // Added border
        borderRadius: 5, // Added border radius
        background: '#e0e0e0', // Light background color
        cursor: 'pointer', // Added pointer cursor
        marginRight: 5 // Added margin to separate buttons
      }}>
        {isActive ? 'Pause' : 'Start'}
      </button>
      <button onClick={handleReset} style={{
        padding: '3px 6px', // Further reduced padding
        fontSize: '0.7em', // Further reduced font size for button text
        border: '1px solid #ccc', // Added border
        borderRadius: 5, // Added border radius
        background: '#e0e0e0', // Light background color
        cursor: 'pointer' // Added pointer cursor
      }}>
        Reset
      </button>
    </div>
  );
};

export default Timer;

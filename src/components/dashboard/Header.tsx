'use client'

import { useRouter } from 'next/navigation'
import Link from 'next/link'
import Image from 'next/image'
import Script from 'next/script'
import { useCredits } from './CreditsContext'
import { useState, useEffect } from 'react'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { Database } from '@/types/supabase'

export default function Header() {
  const { credits } = useCredits()
  const [userName, setUserName] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const supabase = createClientComponentClient<Database>()
  const router = useRouter()

  useEffect(() => {
    const fetchUser = async () => {
      try {
        const { data: { user }, error: userError } = await supabase.auth.getUser()
        if (userError) throw userError
        if (!user) {
          setUserName(null)
          setIsLoading(false)
          return
        }
        // Fetch full_name from profiles table
        const { data: profileData, error: profileError } = await supabase
          .from('profiles')
          .select('full_name')
          .eq('id', user.id)
          .single() as { data: { full_name: string | null } | null, error: any }
        if (profileError) {
          setUserName(null)
        } else {
          setUserName(profileData?.full_name || null)
        }
      } catch (error) {
        setError('Error loading user data')
      } finally {
        setIsLoading(false)
      }
    }
    fetchUser()
  }, [supabase])

  const handleSignOut = async () => {
    try {
      await supabase.auth.signOut()
      window.location.href = '/auth'
    } catch (error) {
      setError('Error signing out')
    }
  }

  if (isLoading) {
    return (
      <header className="fixed top-0 left-0 right-0 z-50 bg-[#0A0A0A] border-b border-white/10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="animate-pulse bg-white/5 h-8 w-24 rounded"></div>
            <div className="animate-pulse bg-white/5 h-8 w-32 rounded"></div>
          </div>
        </div>
      </header>
    )
  }

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-[#0A0A0A] border-b border-white/10">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-end items-center h-16">
          {error && (
            <div className="text-red-500 text-sm mr-4">
              {error}
            </div>
          )}
          <div className="flex items-center space-x-4">
            <Link href="/dashboard/profile" className="text-white hover:text-white/80 transition font-medium flex items-center space-x-2">
              <span>
                {userName ? `Hello, ${userName}` : 'Profile'}
              </span>
              {process.env.NEXT_PUBLIC_SHOW_CREDITS !== "false" && typeof credits === 'number' && (
                <span className="ml-2 px-2 py-1 rounded bg-blue-900 text-blue-200 text-xs font-semibold" title="Credits">
                  {credits} credits
                </span>
              )}
            </Link>
            <button
              onClick={handleSignOut}
              className="ml-4 px-3 py-1 rounded bg-[#800000] text-white text-sm font-semibold hover:bg-[#660000] transition"
              type="button"
            >
              Sign Out
            </button>
          </div>
        </div>
      </div>
    </header>
  )
}

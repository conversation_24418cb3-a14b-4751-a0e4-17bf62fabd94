'use client'

import { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'

type CreditsContextType = {
  credits: number | null
  setCredits: (c: number | null) => void
  refreshCredits: () => Promise<void>
}

const CreditsContext = createContext<CreditsContextType | undefined>(undefined)

export function useCredits() {
  const ctx = useContext(CreditsContext)
  if (!ctx) throw new Error('useCredits must be used within a CreditsProvider')
  return ctx
}

export function CreditsProvider({ children }: { children: ReactNode }) {
  const [credits, setCredits] = useState<number | null>(null)
  const supabase = createClientComponentClient()

  // Fetch credits from Supabase
  const refreshCredits = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) {
        setCredits(null)
        return
      }
      const { data: profile } = await supabase
        .from('profiles')
        .select('credits')
        .eq('id', user.id)
        .single()
      setCredits(profile?.credits ?? 0)
    } catch {
      setCredits(null)
    }
  }

  useEffect(() => {
    refreshCredits()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  return (
    <CreditsContext.Provider value={{ credits, setCredits, refreshCredits }}>
      {children}
    </CreditsContext.Provider>
  )
}

'use client'

import { useEffect } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { useCredits } from './CreditsContext'
import { isPageLocked } from '@/lib/utils'

export default function LockedPageGuard() {
  const { credits } = useCredits()
  const router = useRouter()
  const pathname = usePathname()

  useEffect(() => {
    // Only run on client
    if (typeof window === 'undefined') return

    // Bypass redirect if smartmarkJustMarked flag is set in sessionStorage
    if (window.sessionStorage.getItem('smartmarkJustMarked') === 'true') {
      // Clear the flag so it only bypasses once
      window.sessionStorage.removeItem('smartmarkJustMarked')
      return
    }

    // Check if current page is locked
    const locked = isPageLocked(pathname)
    if (locked && (credits !== null && credits <= 0)) {
      // Redirect to billing if on a locked page and credits are 0 or less
      router.replace('/dashboard/billing')
    }
  }, [credits, pathname, router])

  return null
}

import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs'
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export async function middleware(req: NextRequest) {
  const res = NextResponse.next()
  const supabase = createMiddlewareClient({ req, res })

  // Refresh session if expired - required for Server Components
  const { data: { session } } = await supabase.auth.getSession()

  // Get locked pages from environment
  const lockedPages = process.env.NEXT_PUBLIC_LOCKED_PAGES?.split(',') || []

  // If user is not signed in and trying to access protected routes, redirect to login
  if (!session && req.nextUrl.pathname.startsWith('/dashboard')) {
    return NextResponse.redirect(new URL('/auth', req.url))
  }

  // Check if requested path is locked
  if (session && lockedPages.includes(req.nextUrl.pathname)) {
    // Check user credits
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('credits')
      .eq('id', session.user.id)
      .single()

    if (profileError || !profile || profile.credits === undefined || profile.credits <= 0) {
      // If user has no credits, redirect to billing
      return NextResponse.redirect(new URL('/dashboard/billing', req.url))
    }

    // Optionally, keep the subscription check if you want both credits and subscription to gate access
    // const { data: subscription, error } = await supabase
    //   .from('customer_subscriptions')
    //   .select('status')
    //   .eq('user_id', session.user.id)
    //   .eq('status', 'active')
    //   .single()
    // if (error || !subscription) {
    //   return NextResponse.redirect(new URL('/dashboard/billing', req.url))
    // }
  }

  // If user is signed in and trying to access auth pages, redirect to dashboard
  if (session && (req.nextUrl.pathname === '/auth' || req.nextUrl.pathname === '/')) {
    // console.log('Redirecting authenticated user to dashboard', { // Commented out for security
    //   pathname: req.nextUrl.pathname,
    //   session: session.user,
    //   headers: Object.fromEntries(req.headers.entries())
    // })
    const redirectUrl = new URL('/dashboard', req.url)
    // console.log('Redirect URL:', redirectUrl.toString()) // Commented out for security
    const response = NextResponse.redirect(redirectUrl)
    // console.log('Response headers:', Object.fromEntries(response.headers.entries())) // Commented out for security
    return response
  }

  return res
}

// Specify which routes should be handled by the middleware
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public (public files)
     * - api (API routes)
     */
    '/((?!_next/static|_next/image|favicon.ico|public|api).*)',
  ],
}

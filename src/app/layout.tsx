import './globals.css'
import { Inter } from 'next/font/google'

const inter = Inter({ subsets: ['latin'] })

export const metadata = {
  title: 'TestPaperHero - Free Test Papers & Tuition Resources in Singapore',
  description: 'Automate marking for Singapore Primary P1-P6 test papers and make practice easy.',
  keywords: ['math solver', 'math tuition primary', 'math tuition', 'free test papers', 'sg test paper', 'singapore test papers', 'free test papers singapore', 'free exam papers', 'past year exam paper', 'sg exam papers', 'singapore exam papers'],
  openGraph: {
    title: 'TestPaperHero - Free Test Papers & Tuition Resources in Singapore',
    description: 'Automate marking for Singapore Primary P1-P6 test papers and make practice easy.',
    type: 'website',
    url: 'https://testpaperhero.com',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'TestPaperHero - Free Test Papers & Tuition Resources in Singapore',
    description: 'Automate marking for Singapore Primary P1-P6 test papers and make practice easy.',
  },
  alternates: {
    canonical: 'https://testpaperhero.com',
  },
  metadataBase: new URL('https://testpaperhero.com'),
  icons: {
    icon: '/favicon.ico',
  },
  robots: {
    index: true,
    follow: true,
  },
  // Additional structured data can be added in the component below
}

import Script from 'next/script'
import Header from './components/Header'

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "TestPaperHero",
    "url": "https://testpaperhero.com",
    "description": "Automate marking for Singapore Primary P1-P6 test papers and make practice easy.",
    "keywords": "math solver, math tuition primary, math tuition, free test papers, sg test paper, singapore test papers, free test papers singapore, free exam papers, past year exam paper, sg exam papers, singapore exam papers"
  };

  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <Script
          id="structured-data"
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
          strategy="afterInteractive"
        />
        {/* Umami Tracking Script */}
        <script async src="https://sites.leanalyticsmedia.com/script.js" data-website-id={process.env.NEXT_PUBLIC_UMAMI_WEBSITE_ID}></script>
        {/* PostHog Tracking Script */}
        <Script
          id="posthog-tracking"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              !function(t,e){var o,n,p,r;e.__SV||(window.posthog=e,e._i=[],e.init=function(i,s,a){function g(t,e){var o=e.split(".");2==o.length&&(t=t[o[0]],e=o[1]),t[e]=function(){t.push([e].concat(Array.prototype.slice.call(arguments,0)))}}(p=t.createElement("script")).type="text/javascript",p.crossOrigin="anonymous",p.async=!0,p.src=s.api_host.replace(".i.posthog.com","-assets.i.posthog.com")+"/static/array.js",(r=t.getElementsByTagName("script")[0]).parentNode.insertBefore(p,r);var u=e;for(void 0!==a?u=e[a]=[]:a="posthog",u.people=u.people||[],u.toString=function(t){var e="posthog";return"posthog"!==a&&(e+="."+a),t||(e+=" (stub)"),e},u.people.toString=function(){return u.toString(1)+".people (stub)"},o="init me ys Ss gs ws capture je Di xs register register_once register_for_session unregister unregister_for_session Rs getFeatureFlag getFeatureFlagPayload isFeatureEnabled reloadFeatureFlags updateEarlyAccessFeatureEnrollment getEarlyAccessFeatures on onFeatureFlags onSurveysLoaded onSessionId getSurveys getActiveMatchingSurveys renderSurvey canRenderSurvey canRenderSurveyAsync identify setPersonProperties group resetGroups setPersonPropertiesForFlags resetPersonPropertiesForFlags setGroupPropertiesForFlags resetGroupPropertiesForFlags reset get_distinct_id getGroups get_session_id get_session_replay_url alias set_config startSessionRecording stopSessionRecording sessionRecordingStarted captureException loadToolbar get_property getSessionProperty Is ks createPersonProfile Ps bs opt_in_capturing opt_out_capturing has_opted_in_capturing has_opted_out_capturing clear_opt_in_out_capturing $s debug Es getPageViewId captureTraceFeedback captureTraceMetric".split(" "),n=0;n<o.length;n++)g(u,o[n]);e._i.push([i,s,a])},e.__SV=1)}(document,window.posthog||[]);
              posthog.init('${process.env.NEXT_PUBLIC_POSTHOG_API_KEY}', {
                api_host: '${process.env.NEXT_PUBLIC_POSTHOG_API_HOST}',
                person_profiles: 'identified_only', // or 'always' to create profiles for anonymous users as well
              })
            `,
          }}
        />
        {/* Google tag (gtag.js) */}
        <Script async src={`https://www.googletagmanager.com/gtag/js?id=${process.env.NEXT_PUBLIC_GOOGLE_TAG_ID}`} strategy="afterInteractive" />
        <Script
          id="google-analytics"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', '${process.env.NEXT_PUBLIC_GOOGLE_TAG_ID}');
            `,
          }}
        />
      </head>
      <body className={`${inter.className} min-h-screen bg-primary text-secondary antialiased`} suppressHydrationWarning>
        <Header />
        {children}
      </body>
    </html>
  )
}

import { NextResponse } from 'next/server'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'

export async function POST(req: Request) {
  const supabase = createRouteHandlerClient({ cookies })
  const { amount = 1 } = await req.json()

  // Get the authenticated user
  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser()

  if (userError || !user) {
    return NextResponse.json({ success: false, error: 'Not authenticated' }, { status: 401 })
  }

  // Atomically decrement credits if enough credits are available
  const { data, error } = await supabase.rpc('decrement_user_credits', {
    user_id_to_update: user.id,
    credits_to_deduct: amount,
  })

  if (error) {
    return NextResponse.json({ success: false, error: error.message }, { status: 500 })
  }

  if (data === true) {
    return NextResponse.json({ success: true })
  } else {
    return NextResponse.json({ success: false, error: 'Insufficient credits' }, { status: 400 })
  }
}

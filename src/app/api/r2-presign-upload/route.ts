import { NextRequest, NextResponse } from "next/server";
import { S3Client, PutObjectCommand } from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";

// Use placeholder values for R2 credentials
const R2_ACCESS_KEY_ID = process.env.R2_ACCESS_KEY_ID || "YOUR_R2_ACCESS_KEY_ID_HERE";
const R2_SECRET_ACCESS_KEY = process.env.R2_SECRET_ACCESS_KEY || "YOUR_R2_SECRET_ACCESS_KEY_HERE";
const R2_BUCKET = process.env.R2_BUCKET || "YOUR_R2_BUCKET_NAME_HERE";
const R2_ENDPOINT = process.env.R2_ENDPOINT || "https://youraccountid.r2.cloudflarestorage.com";

const s3Client = new S3Client({
  region: "auto",
  endpoint: R2_ENDPOINT,
  credentials: {
    accessKeyId: R2_ACCESS_KEY_ID,
    secretAccessKey: R2_SECRET_ACCESS_KEY,
  },
});

export async function POST(req: NextRequest) {
  try {
    const { filename, contentType } = await req.json();

    if (!filename) {
      return NextResponse.json({ error: "Missing filename" }, { status: 400 });
    }

    const key = `uploads/${Date.now()}-${filename}`;

    const command = new PutObjectCommand({
      Bucket: R2_BUCKET,
      Key: key,
      ContentType: contentType || "application/pdf",
    });

    const url = await getSignedUrl(s3Client, command, { expiresIn: 60 * 5 }); // 5 minutes

    return NextResponse.json({ url, key });
  } catch (error: any) {
    return NextResponse.json({ error: error.message || "Internal server error" }, { status: 500 });
  }
}

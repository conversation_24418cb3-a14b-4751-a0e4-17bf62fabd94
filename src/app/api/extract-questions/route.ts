import { NextRequest, NextResponse } from "next/server";
import { S3Client, GetObjectCommand } from "@aws-sdk/client-s3";

// Use GoogleGenAI SDK if available, else fallback to fetch
let GoogleGenAI: any = null;
try {
  // @ts-ignore
  GoogleGenAI = require("@google/genai").GoogleGenAI;
} catch (e) {}

const GEMINI_API_KEY = process.env.GEMINI_API_KEY;
const R2_ACCESS_KEY_ID = process.env.R2_ACCESS_KEY_ID;
const R2_SECRET_ACCESS_KEY = process.env.R2_SECRET_ACCESS_KEY;
const R2_BUCKET = process.env.R2_BUCKET;
const R2_ENDPOINT = process.env.R2_ENDPOINT;

if (!R2_ACCESS_KEY_ID || !R2_SECRET_ACCESS_KEY || !R2_BUCKET || !R2_ENDPOINT) {
  throw new Error("Missing required R2 environment variables");
}

const s3Client = new S3Client({
  region: "auto",
  endpoint: R2_ENDPOINT,
  credentials: {
    accessKeyId: R2_ACCESS_KEY_ID as string,
    secretAccessKey: R2_SECRET_ACCESS_KEY as string,
  },
});

async function getR2ObjectBase64(key: string): Promise<string> {
  const command = new GetObjectCommand({
    Bucket: R2_BUCKET,
    Key: key,
  });
  const response = await s3Client.send(command);
  // Read the stream into a buffer
  const chunks: any[] = [];
  for await (const chunk of response.Body as any) {
    chunks.push(chunk);
  }
  const buffer = Buffer.concat(chunks);
  return buffer.toString("base64");
}

export async function POST(req: NextRequest) {
  if (req.method !== "POST") {
    return NextResponse.json({ error: `Method Not Allowed` }, { status: 405 });
  }

  const { base64Pdf, r2Key } = await req.json();

  let pdfBase64 = base64Pdf;

  if (!pdfBase64 && r2Key) {
    try {
      pdfBase64 = await getR2ObjectBase64(r2Key);
    } catch (err) {
      console.error("Failed to fetch PDF from R2:", err);
      return NextResponse.json({ error: "Failed to fetch PDF from storage" }, { status: 500 });
    }
  }

  if (!pdfBase64) {
    return NextResponse.json({ error: "Missing PDF data (base64Pdf or r2Key) in request body" }, { status: 400 });
  }

  try {
    const contents = [
      { text: `You are an exam answer sheet extractor. 
From the attached PDF, extract the answer keys and answers as follows:
- For the first set of questions in a table (with headers like Q1, Q2, ... Q8), use the table header as the key (e.g., "Q1", "Q2", ..., "Q8").
- For the remaining questions, use the question number as the key, prefixed with "Q" and with any sub-question letter (e.g., "Q9", "Q10", ..., "Q15b", "Q17a", "Q17b", "Q18a").
- Remove any trailing periods or spaces from the keys.
- For each key, extract the answer as it appears in the answer sheet.
- Also extract the full question text for each key if available.

Return a JSON object in this format:
{
  "keys": ["Q1", "Q2", ..., "Q8", "Q9", ..., "Q15b", "Q17a", ...],
  "questions": {
    "Q1": "full question text for Q1",
    "Q2": "full question text for Q2",
    ...
    "Q15b": "full question text for Q15b",
    "Q17a": "full question text for Q17a"
  },
  "answers": {
    "Q1": "answer1",
    "Q2": "answer2",
    ...
    "Q15b": "answer for Q15b",
    "Q17a": "answer for Q17a"
  }
}
Do NOT invent or guess any answers, questions, or keys. Only extract what is present in the PDF. If a key is present but has no answer, set its value to an empty string. If a key is present but has no question text, set its value to an empty string. Return ONLY the JSON object, nothing else.` },
      {
        inlineData: {
          mimeType: "application/pdf",
          data: pdfBase64,
        },
      },
    ];

    let response: any;
    if (GoogleGenAI) {
      const ai = new GoogleGenAI({ apiKey: GEMINI_API_KEY });
      response = await ai.models.generateContent({
        model: "gemini-2.0-flash",
        contents: contents,
      });
    } else {
      // fallback to fetch
      const geminiApiUrl = "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=" + GEMINI_API_KEY;
      response = await fetch(geminiApiUrl, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ contents }),
      }).then(r => r.json());
      response.text = response?.candidates?.[0]?.content?.parts?.[0]?.text || "";
    }

    // Try to parse the JSON from Gemini's response
    let keys: string[] = [];
    let answers: Record<string, string> = {};
    let questions: Record<string, string> = {};
    let text = response.text;
    try {
      // Find the first JSON object in the response
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        keys = Array.isArray(parsed.keys) ? parsed.keys : [];
        answers = typeof parsed.answers === "object" ? parsed.answers : {};
        questions = typeof parsed.questions === "object" ? parsed.questions : {};
        // Normalize keys: if key is a number or number+letter, prefix with "Q"
        const normalizeKey = (k: string) => {
          if (/^Q\d+[a-zA-Z]*$/.test(k)) return k;
          if (/^\d+[a-zA-Z]*$/.test(k)) return "Q" + k;
          return k;
        };
        const normalizedKeys = keys.map(normalizeKey);
        const normalizedAnswers: Record<string, string> = {};
        const normalizedQuestions: Record<string, string> = {};
        keys.forEach((k, i) => {
          const normKey = normalizeKey(k);
          normalizedAnswers[normKey] = answers[k];
          normalizedQuestions[normKey] = questions[k] || "";
        });
        keys = normalizedKeys;
        answers = normalizedAnswers;
        questions = normalizedQuestions;
      }
    } catch (e) {
      // fallback: return empty
      keys = [];
      answers = {};
      questions = {};
    }

    return NextResponse.json({ keys, answers, questions, geminiRaw: text });
  } catch (error: any) {
    console.error("Error in extract-questions API:", error);
    return NextResponse.json({ error: "Internal server error during question extraction" }, { status: 500 });
  }
}

import { NextRequest, NextResponse } from "next/server";
import { S3Client, GetObjectCommand } from "@aws-sdk/client-s3";

// Use GoogleGenAI SDK if available, else fallback to fetch
let GoogleGenAI: any = null;
try {
  // @ts-ignore
  GoogleGenAI = require("@google/genai").GoogleGenAI;
} catch (e) {}

const GEMINI_API_KEY = process.env.GEMINI_API_KEY;
const R2_ACCESS_KEY_ID = process.env.R2_ACCESS_KEY_ID;
const R2_SECRET_ACCESS_KEY = process.env.R2_SECRET_ACCESS_KEY;
const R2_BUCKET = process.env.R2_BUCKET;
const R2_ENDPOINT = process.env.R2_ENDPOINT;

if (!R2_ACCESS_KEY_ID || !R2_SECRET_ACCESS_KEY || !R2_BUCKET || !R2_ENDPOINT) {
  throw new Error("Missing required R2 environment variables");
}

const s3Client = new S3Client({
  region: "auto",
  endpoint: R2_ENDPOINT,
  credentials: {
    accessKeyId: R2_ACCESS_KEY_ID as string,
    secretAccessKey: R2_SECRET_ACCESS_KEY as string,
  },
});

async function getR2ObjectBase64(key: string): Promise<string> {
  const command = new GetObjectCommand({
    Bucket: R2_BUCKET,
    Key: key,
  });
  const response = await s3Client.send(command);
  // Read the stream into a buffer
  const chunks: any[] = [];
  for await (const chunk of response.Body as any) {
    chunks.push(chunk);
  }
  const buffer = Buffer.concat(chunks);
  return buffer.toString("base64");
}

export async function POST(req: NextRequest) {
  if (req.method !== "POST") {
    return NextResponse.json({ error: `Method Not Allowed` }, { status: 405 });
  }

  const { base64Pdf, r2Key, answers, answerSheet, questions } = await req.json();

  let pdfBase64 = base64Pdf;

  if (!pdfBase64 && r2Key) {
    try {
      pdfBase64 = await getR2ObjectBase64(r2Key);
    } catch (err) {
      console.error("Failed to fetch PDF from R2:", err);
      return NextResponse.json({ error: "Failed to fetch PDF from storage" }, { status: 500 });
    }
  }

  if (!pdfBase64 || !answers || !answerSheet || !questions) {
    return NextResponse.json({ error: "Missing PDF data (base64Pdf or r2Key), answers, answerSheet, or questions in request body" }, { status: 400 });
  }

  try {
    // Build a complete userAnswers object with all keys from the answer sheet
    const completeUserAnswers: Record<string, string> = {};
    for (const key of Object.keys(answerSheet)) {
      completeUserAnswers[key] = (answers && answers[key] !== undefined) ? answers[key] : "";
    }

    // Batching logic: split questions into batches of 2
    const allKeys = Object.keys(answerSheet);
    const batchSize = 2;
    const batches: string[][] = [];
    for (let i = 0; i < allKeys.length; i += batchSize) {
      batches.push(allKeys.slice(i, i + batchSize));
    }

    const batchResults: Record<string, { correct: boolean | null, explanation: string }> = {};
    let allRaw = "";

    // Parallelize batch processing
    const batchPromises = batches.map((batch) => {
      // Build batch-specific prompt and context
      const batchAnswerSheet: Record<string, string> = {};
      const batchUserAnswers: Record<string, string> = {};
      const batchQuestions: Record<string, string> = {};
      for (const key of batch) {
        batchAnswerSheet[key] = answerSheet[key];
        batchUserAnswers[key] = completeUserAnswers[key];
        batchQuestions[key] = questions[key] || "";
      }

      const markingPrompt = `
You are an exam marker. You are given:
- The full PDF of the exam paper, including all images, charts, and figures.
- An answer sheet mapping (JSON object) with keys (e.g. Q1, Q2, Q17a, etc.) and their correct answers.
- A set of user answers (JSON object) with the same keys.
- A mapping of question keys to the full question text (JSON object).

For each question key, do the following:
- Compare the user's answer to the correct answer for this question.
- Mark as correct if the user's answer is contextually similar in meaning to the correct answer, even if the wording is different. For example, "Mary caught a fish and cooked it." and "Mary cooked the fish she caught." should both be marked correct. Only mark as incorrect if the user's answer is factually wrong or misses key information.
- Do NOT invent, guess, or use any information not present in the correct answer, question text, or the PDF.
- For each key, output:
  - "correct: yes" or "correct: no"
  - "explanation:"
    - ALWAYS provide a detailed, step-by-step explanation, whether the answer is correct, incorrect, or unanswered.
    - Write your explanation in simple, easy-to-understand English, using point form or numbered steps.
    - Do NOT start with "The correct answer is..." or "The user's answer is...". Go straight to the reasoning and working.
    - Be concise and to the point. Use simple terms and avoid unnecessary detail.
    - The explanation must teach the user how to arrive at the correct answer, including:
      - A breakdown of the reasoning, calculations, or logic needed to solve the question, referencing the question text AND the PDF (including any images, charts, or figures).
      - Show all working steps, formulas, or logic in a clear, instructional manner (use bullet points or numbered steps).
      - If the question is conceptual, explain the concept and why the user's answer is incorrect or missing.
      - If the question is multi-step, walk through each step.
      - The goal is to help the user learn how to solve the question, not just state the answer.
      - If the answer is correct, still provide a full worked solution and explanation.
      - You MUST analyze the PDF for this question, and reference any relevant images, charts, or figures in your explanation.
- Format your output as a clear, user-friendly markdown block, one block per key, using this format:

**question [KEY]:**
**correct:** yes/no
**explanation:**
[Your explanation in simple, point form English, starting directly with the reasoning/working]

- Ensure EVERY question (correct, incorrect, or unanswered) has a detailed, instructional explanation as above, and always reference the PDF (including images/figures) for your reasoning.
- Do NOT hallucinate, do NOT invent answers, do NOT use any information not present in the answerSheet mapping, question text, or the PDF.
- If the question text is not available, base your explanation on the answer sheet and the PDF only.

I repeat: You must strictly follow all instructions above for every question. Do not deviate from the required format or marking logic. If you do not follow these instructions exactly, your response will be rejected.
`;

      const markingContents = [
        { text: markingPrompt },
        { text: `Answer Sheet Mapping (JSON):\n${JSON.stringify(batchAnswerSheet, null, 2)}` },
        { text: `User Answers (JSON):\n${JSON.stringify(batchUserAnswers, null, 2)}` },
        { text: `Question Texts (JSON):\n${JSON.stringify(batchQuestions, null, 2)}` },
        {
          inlineData: {
            mimeType: "application/pdf",
            data: pdfBase64,
          },
        },
      ];

      // Helper to run Gemini call with timeout
      async function runMarking() {
        let markingResponse: any;
        if (GoogleGenAI) {
          const ai = new GoogleGenAI({ apiKey: GEMINI_API_KEY });
          markingResponse = await Promise.race([
            ai.models.generateContent({
              model: "gemini-2.0-flash",
              contents: markingContents,
            }),
            new Promise((_, reject) =>
              setTimeout(() => reject(new Error("Gemini marking timed out (25s limit)")), 25000)
            )
          ]);
        } else {
          // fallback to fetch
          const geminiApiUrl = "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=" + GEMINI_API_KEY;
          markingResponse = await fetch(geminiApiUrl, {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ contents: markingContents }),
          }).then(r => r.json());
          markingResponse.text = markingResponse?.candidates?.[0]?.content?.parts?.[0]?.text || "";
        }
        return markingResponse.text;
      }

      // Retry logic: if batch fails, try each question individually
      return runMarking().catch(async (err) => {
        // If batch fails, try each question in the batch individually
        const singleResults: string[] = [];
        for (const key of batch) {
          try {
            // Use the markSingleQuestion function from src/lib/marking.ts if available
            // For now, replicate logic inline
            const singlePrompt = `
You are an exam marker. You are given:
- The full PDF of the exam paper, including all images, charts, and figures.
- The question key: ${key}
- The full question text: "${batchQuestions[key]}"
- The correct answer: "${batchAnswerSheet[key]}"
- The user's answer: "${batchUserAnswers[key]}"

IMPORTANT:
- You MUST reference the question text, the correct answer, and the PDF (including any images, charts, or figures) in your explanation.
- You MUST analyze the PDF and describe any relevant images, charts, or figures that help solve the question.
- If you do not reference the PDF, question, and correct answer for every explanation, your response will be rejected.
- If the user's answer is blank, provide a full, step-by-step worked solution as if teaching a student from scratch, referencing the question, correct answer, and PDF.

Your job:
- Compare the user's answer to the correct answer for this question.
- Mark as correct if the user's answer is contextually similar in meaning to the correct answer, even if the wording is different. For example, "Mary caught a fish and cooked it." and "Mary cooked the fish she caught." should both be marked correct. Only mark as incorrect if the user's answer is factually wrong or misses key information.
- Do NOT invent, guess, or use any information not present in the correct answer, question text, or the PDF.
- Output:
  - "correct: yes" or "correct: no"
  - "explanation:"
    - ALWAYS provide a detailed, step-by-step explanation, whether the answer is correct, incorrect, or unanswered.
    - Write your explanation in simple, easy-to-understand English, using point form or numbered steps.
    - Do NOT start with "The correct answer is..." or "The user's answer is...". Go straight to the reasoning and working.
    - Be concise and to the point. Use simple terms and avoid unnecessary detail.
    - The explanation must teach the user how to arrive at the correct answer, including:
      - A breakdown of the reasoning, calculations, or logic needed to solve the question, referencing the question text AND the PDF (including any images, charts, or figures).
      - Show all working steps, formulas, or logic in a clear, instructional manner (use bullet points or numbered steps).
      - If the question is conceptual, explain the concept and why the user's answer is incorrect or missing.
      - If the question is multi-step, walk through each step.
      - The goal is to help the user learn how to solve the question, not just state the answer.
      - If the answer is correct, still provide a full worked solution and explanation.
      - You MUST analyze the PDF for this question, and reference any relevant images, charts, or figures in your explanation.
- Format your output as a clear, user-friendly markdown block, using this format:

**question [${key}]:**
**correct:** yes/no
**explanation:**
[Your explanation in simple, point form English, starting directly with the reasoning/working]
`;
            const singleContents = [
              { text: singlePrompt },
              {
                inlineData: {
                  mimeType: "application/pdf",
                  data: pdfBase64,
                },
              },
            ];
            let singleResponse: any;
            if (GoogleGenAI) {
              const ai = new GoogleGenAI({ apiKey: GEMINI_API_KEY });
              singleResponse = await Promise.race([
                ai.models.generateContent({
                  model: "gemini-2.0-flash",
                  contents: singleContents,
                }),
                new Promise((_, reject) =>
                  setTimeout(() => reject(new Error("Gemini marking timed out (25s limit)")), 25000)
                )
              ]);
              return singleResponse.text;
            } else {
              const geminiApiUrl = "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=" + GEMINI_API_KEY;
              singleResponse = await fetch(geminiApiUrl, {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({ contents: singleContents }),
              }).then(r => r.json());
              return singleResponse?.candidates?.[0]?.content?.parts?.[0]?.text || "";
            }
          } catch (singleErr) {
            // If even single question fails, return error message
            return `**question [${key}]:**\n**correct:** null\n**explanation:** Marking failed: ${String(singleErr)}`;
          }
        }
        // Wait for all single question results
        return (await Promise.all(singleResults));
      });
    });

    // Wait for all batches to complete (parallel)
    const batchSettled = await Promise.allSettled(batchPromises);

    // Parse results
    for (const batchResult of batchSettled) {
      if (batchResult.status === "fulfilled") {
        const markingText = Array.isArray(batchResult.value) ? batchResult.value.join("\n\n") : batchResult.value;
        allRaw += markingText + "\n\n";
        // Parse markdown-style Gemini output for per-question marking
        const questionBlocks = markingText.split(/\*\*question\s+([A-Za-z0-9]+)\:\*\*/g).slice(1);
        for (let i = 0; i < questionBlocks.length; i += 2) {
          const qKey = questionBlocks[i].trim();
          const block = questionBlocks[i + 1] || "";
          // Extract correct/incorrect
          const correctMatch = block.match(/\*\*correct\:\*\*\s*(yes|no|null)/i);
          let correct: boolean | null = null;
          if (correctMatch) {
            if (/yes/i.test(correctMatch[1])) correct = true;
            else if (/no/i.test(correctMatch[1])) correct = false;
            else correct = null;
          }
          // Extract explanation
          const explanationMatch = block.match(/\*\*explanation\:\*\*\s*([\s\S]*?)(?=\*\*question\s+[A-Za-z0-9]+\:\*\*|$)/i);
          let explanation = explanationMatch ? explanationMatch[1].trim() : "";
          if (!explanation) explanation = "No explanation provided.";
          if (qKey) {
            batchResults[qKey] = { correct, explanation };
          }
        }
      } else {
        // If batch failed, mark all questions in the batch as failed
        // (This should be rare due to retry logic)
        // There is no direct access to the batch keys here, so this should not happen
      }
    }

    // Ensure every question in the answer sheet is present in the marking result
    for (const key of Object.keys(answerSheet)) {
      if (!batchResults[key]) {
        batchResults[key] = {
          correct: null,
          explanation: "No explanation generated."
        };
      }
    }

    // Enforce: blank/whitespace user answers are always marked incorrect
    for (const key of Object.keys(answerSheet)) {
      const userAnswer = (answers && answers[key] !== undefined) ? answers[key] : "";
      if (typeof userAnswer === "string" && userAnswer.trim() === "") {
        batchResults[key].correct = false;
      }
    }

    return NextResponse.json({
      result: batchResults,
      raw: allRaw
    });
  } catch (error: any) {
    console.error("Error in marking API:", error);
    return NextResponse.json({
      error: "Internal server error during marking",
      message: error?.message || String(error),
      stack: error?.stack || null
    }, { status: 500 });
  }
}

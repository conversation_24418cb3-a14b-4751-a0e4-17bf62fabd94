import { NextResponse } from 'next/server';
import { sendEmail } from '@/lib/email';

export async function POST(request: Request) {
  try {
    const { firstName, lastName, email, subject, message } = await request.json();

    if (!firstName || !email || !message) {
      return NextResponse.json(
        { error: 'First name, email, and message are required' },
        { status: 400 }
      );
    }

    const emailSubject = subject || 'New Contact Form Submission';
    const emailBody = `
      <h1>New Contact Form Submission</h1>
      <p><strong>First Name:</strong> ${firstName}</p>
      <p><strong>Last Name:</strong> ${lastName || 'N/A'}</p>
      <p><strong>Email:</strong> ${email}</p>
      <p><strong>Subject:</strong> ${emailSubject}</p>
      <p><strong>Message:</strong></p>
      <p>${message}</p>
    `;

    const result = await sendEmail({
      to: '<EMAIL>',
      subject: emailSubject,
      react: <div dangerouslySetInnerHTML={{ __html: emailBody }} />,
    });

    if (!result.success) {
      return NextResponse.json(
        { error: 'Failed to send contact email', details: result.error },
        { status: 500 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

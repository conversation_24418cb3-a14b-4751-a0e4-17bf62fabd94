import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { NextResponse } from 'next/server'

export async function POST() {
  const supabase = createRouteHandlerClient({ cookies })
  
  try {
    // Sign out from Supabase
    const { error } = await supabase.auth.signOut()
    
    if (error) {
      throw error
    }

    // Clear any session cookies
    const cookieStore = await cookies()
    cookieStore.set('sb-access-token', '', { expires: new Date(0) })
    cookieStore.set('sb-refresh-token', '', { expires: new Date(0) })

    return NextResponse.redirect(new URL('/auth', process.env.NEXT_PUBLIC_SITE_URL), {
      status: 302
    })
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to log out' },
      { status: 500 }
    )
  }
}

import { NextResponse } from 'next/server'
import { headers } from 'next/headers'
import { stripe } from '@/utils/stripe'
import type Stripe from 'stripe'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { createClient } from '@supabase/supabase-js'

const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET!

// Create Supabase admin client using service role key
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function POST(req: Request) {
  // console.log('Webhook received');
  try {
    const body = await req.text()
    // console.log('Webhook body:', body);
    const headersList = await headers()
    const signature = headersList.get('stripe-signature')

    if (!signature) {
      console.error('Missing stripe-signature header');
      return NextResponse.json(
        { error: 'Missing stripe-signature header' },
        { status: 400 }
      )
    }

    const event = stripe.webhooks.constructEvent(
      body,
      signature,
      webhookSecret
    )

    // Use supabaseAdmin for all DB operations
    const supabase = supabaseAdmin

    // Handle the event
    const defaultCredits = parseInt(process.env.NEXT_PUBLIC_INITIAL_USER_CREDITS || "10", 10);

    switch (event.type) {
      case 'checkout.session.completed': {
        const session = event.data.object as Stripe.Checkout.Session

        // Get the subscription
        const subscription = await stripe.subscriptions.retrieve(
          session.subscription as string
        )

        // Validate required fields
        if (!session.metadata?.userId) {
          throw new Error('Missing user ID in session metadata')
        }

        // Create billing portal session
        let portalUrl = null
        try {
          const portalSession = await stripe.billingPortal.sessions.create({
            customer: subscription.customer as string,
            return_url: `https://${process.env.NEXT_PUBLIC_SITE_URL}/dashboard/billing`,
          })
          portalUrl = portalSession.url
        } catch (portalErr) {
          console.error('Error creating billing portal session:', portalErr)
        }

        // Insert subscription data - triggers will handle credit updates
        const { error } = await supabase.from('customer_subscriptions').insert({
          user_id: session.metadata.userId,
          subscription_id: subscription.id,
          status: subscription.status,
          price_id: subscription.items.data[0].price.id,
          quantity: subscription.items.data[0].quantity,
          stripe_portal_url: portalUrl,
          created: new Date().toISOString()
        });

        if (error) {
          throw new Error(`Database insert error: ${error.message}`)
        }

        break
      }

      case 'customer.subscription.updated': {
        const subscription = event.data.object as Stripe.Subscription

        // Update simplified subscription in database
        await supabase
          .from('customer_subscriptions')
          .update({
            status: subscription.status,
            price_id: subscription.items.data[0].price.id
          })
          .eq('subscription_id', subscription.id);

        // Directly renew user credits on subscription renewal if status is active
        if (subscription.status === 'active') {
          // Find the user_id for this subscription
          const { data: subRows, error: subError } = await supabase
            .from('customer_subscriptions')
            .select('user_id')
            .eq('subscription_id', subscription.id)
            .limit(1);

          if (subError) {
            throw new Error(`Error fetching subscription: ${subError.message}`);
          }
          if (!subRows || subRows.length === 0) {
            throw new Error(`No user found for subscription ID: ${subscription.id}`);
          }

          const userId = subRows[0].user_id;
          const priceId = subscription.items.data[0].price.id;

          // Fetch credits from the plan_credits table
          const { data: planCredits, error: planCreditsError } = await supabase
            .from('plan_credits')
            .select('credits')
            .eq('price_id', priceId)
            .limit(1);

          if (planCreditsError) {
            throw new Error(`Error fetching plan credits: ${planCreditsError.message}`);
          }

          let creditsToUpdate = defaultCredits; // Default to initial credits if plan not found
          if (planCredits && planCredits.length > 0) {
            creditsToUpdate = planCredits[0].credits;
          }

          // Update the user's credits in the profiles table
          const { error: updateCreditsError } = await supabase
            .from('profiles')
            .update({ credits: creditsToUpdate })
            .eq('id', userId);

          if (updateCreditsError) {
            throw new Error(`Error updating user credits: ${updateCreditsError.message}`);
          }
        }

        break
      }

      case 'customer.subscription.deleted': {
        const subscription = event.data.object as Stripe.Subscription

        // Update simplified subscription in database
        await supabase
          .from('customer_subscriptions')
          .update({
            status: subscription.status
          })
          .eq('subscription_id', subscription.id)

        // Credits are now updated by a database trigger. No action needed here.
        break
      }

      // --- Handle refunds and disputes: reset user credits to default ---
      case 'charge.refunded':
      case 'charge.dispute.created':
      case 'charge.dispute.funds_withdrawn': {
        // Get the charge object (for refunds/disputes)
        const charge = event.data.object as Stripe.Charge | any;
        const stripeCustomerId = charge.customer;

        if (!stripeCustomerId) {
          throw new Error('Missing Stripe customer ID on charge event');
        }

        // Find the user_id from the customers table
        const { data: customerRows, error: customerError } = await supabase
          .from('customers')
          .select('user_id')
          .eq('stripe_customer_id', stripeCustomerId)
          .limit(1);

        if (customerError) {
          throw new Error(`Error fetching customer: ${customerError.message}`);
        }
        if (!customerRows || customerRows.length === 0) {
          throw new Error(`No user found for Stripe customer ID: ${stripeCustomerId}`);
        }

        const userId = customerRows[0].user_id;

        // Reset the user's credits to the default value
        const { error: updateError } = await supabase
          .from('profiles')
          .update({ credits: defaultCredits })
          .eq('id', userId);

        if (updateError) {
          throw new Error(`Error resetting user credits: ${updateError.message}`);
        }

        // Update the user's active subscription status to 'canceled'
        const { error: subUpdateError } = await supabase
          .from('customer_subscriptions')
          .update({ status: 'canceled' })
          .eq('user_id', userId)
          .eq('status', 'active');

        if (subUpdateError) {
          throw new Error(`Error updating subscription status: ${subUpdateError.message}`);
        }

        // Optionally: log this action or notify admins here

        break;
      }

    }

    return NextResponse.json({ received: true })
  } catch (err) {
    console.error('Error processing webhook:', err)
    // TEMP: Return error details in response for debugging
    return NextResponse.json(
      { error: 'Error processing webhook', details: err instanceof Error ? err.message : String(err), stack: err instanceof Error ? err.stack : undefined },
      { status: 400 }
    )
  }
}

import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { NextResponse } from 'next/server'

import { stripe } from '@/utils/stripe'
import { absoluteUrl } from '@/lib/utils'

const return_url = absoluteUrl('/dashboard/billing')

export async function POST(req: Request) {
  // Accept empty or missing body gracefully
  try {
    await req.json();
  } catch (e) {
    // Ignore if body is empty or invalid JSON
  }
  const cookieStore = cookies()
  const supabase = createRouteHandlerClient({ cookies: () => cookieStore })

  const {
    data: { session },
  } = await supabase.auth.getSession()

  if (!session?.user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  let { data: profile } = await supabase
    .from('profiles')
    .select('stripe_customer_id')
    .eq('id', session?.user.id)
    .single()

  let stripeCustomerId = profile?.stripe_customer_id

  // If missing, search for existing Stripe customer by email, or create if not found
  if (!stripeCustomerId) {
    try {
      const email = session.user.email
      if (!email) {
        return NextResponse.json({ error: 'User email not found' }, { status: 400 })
      }
      // Search for existing Stripe customer by email
      const existingCustomers = await stripe.customers.list({ email, limit: 1 })
      if (existingCustomers.data.length > 0) {
        stripeCustomerId = existingCustomers.data[0].id
      } else {
        const customer = await stripe.customers.create({ email })
        stripeCustomerId = customer.id
      }
      // Update Supabase profile
      await supabase
        .from('profiles')
        .update({ stripe_customer_id: stripeCustomerId })
        .eq('id', session.user.id)
    } catch (err) {
      console.log(err)
      return NextResponse.json({ error: 'Failed to create or find Stripe customer' }, { status: 500 })
    }
  }

  try {
    const stripeSession = await stripe.billingPortal.sessions.create({
      customer: stripeCustomerId,
      return_url,
    })

    return NextResponse.json({ url: stripeSession.url })
  } catch (error: any) {
    console.log(error);
    return NextResponse.json(
      { error: 'Error creating billing portal session' },
      { status: 500 }
    )
  }
}

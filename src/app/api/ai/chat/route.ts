import { NextResponse } from "next/server";
import { S3Client, GetObjectCommand } from "@aws-sdk/client-s3";
import { GoogleGenAI } from "@google/genai";

const ai = new GoogleGenAI({ apiKey: process.env.GEMINI_API_KEY });

const {
  R2_ACCESS_KEY_ID,
  R2_SECRET_ACCESS_KEY,
  R2_BUCKET,
  R2_ENDPOINT,
} = process.env;

const s3Client = new S3Client({
  region: "auto",
  endpoint: R2_ENDPOINT,
  credentials: {
    accessKeyId: R2_ACCESS_KEY_ID!,
    secretAccessKey: R2_SECRET_ACCESS_KEY!,
  },
});

async function getR2ObjectBase64(key: string): Promise<string> {
  const command = new GetObjectCommand({
    Bucket: R2_BUCKET,
    Key: key,
  });
  const response = await s3Client.send(command);
  const chunks: Uint8Array[] = [];
  for await (const chunk of response.Body as any) {
    chunks.push(chunk);
  }
  // Concatenate all Uint8Array chunks
  const totalLength = chunks.reduce((acc, curr) => acc + curr.length, 0);
  const buffer = new Uint8Array(totalLength);
  let offset = 0;
  for (const chunk of chunks) {
    buffer.set(chunk, offset);
    offset += chunk.length;
  }
  // Convert to base64
  const base64 = Buffer.from(buffer).toString("base64");
  return base64;
}

export async function POST(req: Request) {
  try {
    const body = await req.json();
    let {
      base64Pdf,
      r2Key,
      questionKey,
      userAnswer,
      correctAnswer,
      questionText,
      userQuestion,
      explanation,
    } = body;

    let pdfBase64 = base64Pdf;

    if (!pdfBase64 && r2Key) {
      try {
        pdfBase64 = await getR2ObjectBase64(r2Key);
      } catch (err) {
        console.error("Failed to fetch PDF from R2:", err);
        return NextResponse.json(
          { error: "Failed to fetch PDF from storage" },
          { status: 500 }
        );
      }
    }

    if (
      !pdfBase64 ||
      !questionKey ||
      typeof userAnswer !== "string" ||
      typeof correctAnswer !== "string" ||
      typeof questionText !== "string" ||
      typeof userQuestion !== "string"
    ) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    const contents = [
      {
        text: `You are an expert exam marker and explainer. A user has asked a question about a specific answer in a test paper. Here is the context:

Question number: ${questionKey}
Question: ${questionText}
User's answer: ${userAnswer}
Correct answer: ${correctAnswer}
${explanation ? `Explanation: ${explanation}` : ""}

User's question about this answer: "${userQuestion}"

Instructions for your reply:
- You MUST use numbered steps (1., 2., 3., etc) or bullet points (-, •) for every explanation. Do NOT use asterisks (*) for each sentence. Do NOT write in paragraph form.
- Start each step on a new line. Never combine multiple steps into one line.
- ALWAYS break your explanation into clear, numbered or bulleted steps, even for simple questions. Do NOT use dense paragraphs.
- Use simple English as if you are explaining to a primary school student.
- Only provide the explanation or working in clear, simple, step-by-step form.
- Do NOT repeat or restate "The correct answer is..." or "The user's answer is..."
- If the user's question is unclear, just give a helpful, simple explanation for the answer.
- If relevant, use information from the PDF to help answer the question (e.g., show the options for the question if asked).
- Do NOT include any unnecessary details or long-winded steps.
- Respond in markdown if formatting is helpful.

Now, provide your explanation/working below:
`,
      },
      {
        inlineData: {
          mimeType: "application/pdf",
          data: pdfBase64,
        },
      },
    ];

    const response = await ai.models.generateContent({
      model: "gemini-2.0-flash",
      contents: contents,
    });

    const text = response.text;
    return NextResponse.json({ reply: text });
  } catch (error) {
    console.error("Error in ask-ai route:", error);
    return NextResponse.json(
      { error: "Error processing request" },
      { status: 500 }
    );
  }
}

'use client'

import { useState } from 'react';
import Image from 'next/image'
import Link from 'next/link'
import {
  CodeBracketIcon,
  RocketLaunchIcon,
  CpuChipIcon,
  BookOpenIcon,
  EnvelopeIcon,
  PhoneIcon,
  MapPinIcon,
} from '@heroicons/react/24/outline'
import PricingCard from './components/PricingCard'
import FaqItem from './components/FaqItem'
import TestimonialCard from './components/TestimonialCard'
import FeatureCard from './components/FeatureCard'

const features = [
  {
    icon: <CodeBracketIcon className="w-6 h-6" />,
    title: 'Production-Ready Code',
    description: 'Get started with professionally written, well-structured code that scales.',
  },
  {
    icon: <RocketLaunchIcon className="w-6 h-6" />,
    title: 'Quick Deployment',
    description: 'Deploy your application with one click to your favorite cloud platform.',
  },
  {
    icon: <CpuChipIcon className="w-6 h-6" />,
    title: 'AI Integration',
    description: 'Built-in AI capabilities to supercharge your application features.',
  },
]

const pricingPlans = [
  {
    title: "Free",
    price: "0",
    period: "/month",
    description: "Mark up to 5 papers / mo.\nAI explanations for every answer\nNo credit card required",
    features: [
      "Mark up to 5 papers / mo.",
      "AI explanations for every answer",
      "No credit card required"
    ],
    buttonText: "Start Free",
    priceId: ""
  },
  {
    title: "Starter Monthly",
    price: "19",
    period: "/month",
    description: "Stimulate up to 50 papers / mo. Ideal for steady progress with priority marking and reliable email support. Perfect for core subjects English, Math & Science.",
    features: [
      "Stimulate up to 50 papers / mo.",
      "Covers English, Math & Science",
      "Priority marking speed",
      "Email support",
      "Cancel anytime"
    ],
    buttonText: "Choose Starter Plan",
    popular: true,
    priceId: "price_starter"
  },
  {
    title: "Tutors Monthly Plan",
    price: "59",
    period: "/month",
    description: "Stimulate up to 300 papers / mo. Designed for tuition centers to efficiently manage and stimulate large volumes of papers with advanced features and dedicated support.",
    features: [
      "Stimulate up to 300 papers / mo.",
      "Highest priority marking speed",
      "Best value for tuition centers",
      "Email support",
      "Cancel anytime"
    ],
    buttonText: "Choose Tutors Plan",
    priceId: "price_family"
  }
]

const faqs = [
  {
    question: "How does the free plan work?",
    answer: "Every month, you can mark up to 5 papers for free—no credit card required. After that, you can upgrade to a paid plan for more papers."
  },
  {
    question: "What subjects are supported?",
    answer: "TestPaperHero currently supports English, Math, and Science past papers for Singapore primary levels."
  },
  {
    question: "Is my payment data safe?",
    answer: "All payments are securely handled by Stripe, ensuring your payment information is protected with industry-leading security standards. We do not store any payment details on our servers."
  },
  {
    question: "Can I cancel or change my plan anytime?",
    answer: "Yes, you can upgrade, downgrade, or cancel your plan at any time—no lock-in, no hassle."
  },
  {
    question: "How fast are results delivered?",
    answer: "Most papers are marked and returned with feedback in under 1 minute, thanks to our AI-powered system."
  },
  {
    question: "Do you provide any Test Paper?",
    answer: "We do not provide any test papers. Users can upload any PDF test paper they find online to be marked by our AI."
  },
  {
    question: "Does doing past papers help?",
    answer: "Yes, doing past papers helps familiarize students with exam formats, improves time management, and reinforces learning through practice."
  },
  {
    question: "Does your app work with any past paper PDF?",
    answer: "Our app works best with past papers that include answers at the end of the PDF. This ensures the explanations are accurate and match the provided answers, giving you reliable feedback."
  },
  {
    question: "What devices are best for using TestPaperHero?",
    answer: "TestPaperHero is best viewed on wide screens like Tablet or Desktop for the best experience."
  }
]

const testimonials = [
  {
    content: "TestPaperHero has given me my evenings back. Marking used to take hours—now it's done before dinner!",
    author: {
      name: "Mrs. Lim",
      avatar: "/avatars/img_Testimonial_1.webp",
      title: "Parent of P6 Student, Singapore"
    }
  },
  {
    content: "No more arguments over answers. The AI explains everything so clearly, my son is more confident and independent.",
    author: {
      name: "Mr. Tan",
      avatar: "/avatars/img_Testimonial_2.webp",
      title: "Father of P5 Student"
    }
  },
  {
    content: "We tried the free plan and upgraded after a week. It's worth every cent for the time and peace it brings.",
    author: {
      name: "Shanti R.",
      avatar: "/avatars/img_Testimonial_3.webp",
      title: "Mother of 2, Bukit Batok"
    }
  }
]

import Header from './components/Header'

export default function Home() {
  const [submissionMessage, setSubmissionMessage] = useState('');
  return (
    <>
      <Header />
      <main className="min-h-screen bg-[#0A0A0A]">

      {/* Hero Section */}
      <section id="home" className="pt-32 pb-16 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <div className="inline-flex items-center px-3 py-1 rounded-full bg-white/5 border border-white/10 text-sm text-white/70 mb-6">
                <span className="mr-2">🎓</span> English, Maths, Science Solver for Singapore Test Papers
              </div>
              <h1 className="text-4xl sm:text-5xl font-bold text-white leading-[1.1] tracking-tight mb-6">
Conquer Singapore P1-P6 Test Papers Confidently with Real Exam Simulations and AI Marking
</h1>
              <p className="text-lg text-white/70 mb-8 leading-relaxed">
                Our AI app simulates Singapore primary school test papers, marks automatically, and provides explanations to unlock every child potential.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <a
                  href="/auth"
                  className="inline-flex justify-center items-center px-6 py-3 rounded-lg bg-[#FFBE1A] text-black font-medium hover:bg-[#FFBE1A]/90 transition-colors"
                >
                  Try It Free
                </a>
                <a
                  href="#how"
                  className="inline-flex justify-center items-center px-6 py-3 rounded-lg border border-white/10 text-white font-medium hover:bg-white/5 transition-colors"
                >
                  How It Works
                </a>
              </div>
              {/*
              <div className="flex items-center space-x-4 mt-8">
                <div className="flex items-center bg-white/5 px-3 py-1 rounded-full">
                  <div className="flex">
                    {'★★★★★'.split('').map((star, i) => (
                      <span key={i} className="text-[#FFBE1A]">
                        {star}
                      </span>
                    ))}
                  </div>
                  <span className="ml-2 text-[#FFBE1A] font-medium">4.9/5</span>
                  <span className="mx-2 text-white/30">•</span>
                  <span className="text-white/70">Over 547+ parents have trusted this marking app</span>
                </div>
              </div>
              */}
            </div>
            <div className="flex items-center justify-center mt-8 lg:mt-0">
              {/* Dropzone-style clickable area for upload, redirects to /auth */}
              <div
                onClick={() => window.location.href = '/auth'}
                tabIndex={0}
                role="button"
                aria-label="Upload PDF"
                onKeyDown={e => { if (e.key === 'Enter' || e.key === ' ') window.location.href = '/auth'; }}
                style={{ borderRadius: 12, overflow: 'hidden' }}
              >
                <Image src="/img_Hero.png" alt="Hero Image" width={400} height={300} style={{ opacity: 0.85 }} />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Why Section (Pain Points) */}
      <section id="why" className="py-20 px-4 bg-[#18181B]">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-4xl font-bold text-white text-center mb-12">
            Why Parents Choose TestPaperHero
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Time Burden */}
            <div className="bg-[#18181B] rounded-2xl p-8 flex flex-col items-center text-center border border-white/5">
              <div className="w-14 h-14 flex items-center justify-center rounded-full bg-[#FFBE1A]/10 mb-4">
                <svg className="w-8 h-8 text-[#FFBE1A]" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M12 8v4l3 2m6-2a10 10 0 11-20 0 10 10 0 0120 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">Exam Format Practice</h3>
              <p className="text-white/70">
                Practice past year papers to familiarize your child with the exam format and question types, building confidence for the actual test.
              </p>
            </div>
            {/* High Tuition Costs */}
            <div className="bg-[#18181B] rounded-2xl p-8 flex flex-col items-center text-center border border-white/5">
              <div className="w-14 h-14 flex items-center justify-center rounded-full bg-[#FFBE1A]/10 mb-4">
                <svg className="w-8 h-8 text-[#FFBE1A]" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M12 8c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4zm0 0V4m0 16v-4" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">Simulate Real Exam Scenarios</h3>
              <p className="text-white/70">
                Time your child during practice sessions to simulate real exam scenarios, helping them improve time management skills under pressure.
              </p>
            </div>
            {/* Stress & Emotional Toll */}
            <div className="bg-[#18181B] rounded-2xl p-8 flex flex-col items-center text-center border border-white/5">
              <div className="w-14 h-14 flex items-center justify-center rounded-full bg-[#FFBE1A]/10 mb-4">
                <svg className="w-8 h-8 text-[#FFBE1A]" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M9.75 9.75a3 3 0 014.5 0M12 15v.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">Identify and Fix Weak Areas</h3>
              <p className="text-white/70">
                Use past papers to identify your child's weak areas and misconceptions, allowing for targeted revision and improvement.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works / Benefits Section */}
      <section id="how" className="py-20 px-4">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-4xl font-bold text-white text-center mb-12">
            How It Works — Mark Papers in 3 Clicks
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
            {/* Step 1 */}
            <div className="bg-[#18181B] rounded-2xl p-8 flex flex-col items-center text-center border border-white/5">
              <div className="w-14 h-14 flex items-center justify-center rounded-full bg-[#FFBE1A]/10 mb-4">
                <svg className="w-8 h-8 text-[#FFBE1A]" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M12 4v16m8-8H4" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">1. Upload PDF</h3>
              <p className="text-white/70">Select your child’s practice test paper and upload it securely to our platform.</p>
            </div>
            {/* Step 2 */}
            <div className="bg-[#18181B] rounded-2xl p-8 flex flex-col items-center text-center border border-white/5">
              <div className="w-14 h-14 flex items-center justify-center rounded-full bg-[#FFBE1A]/10 mb-4">
                <svg className="w-8 h-8 text-[#FFBE1A]" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M9 12l2 2 4-4m5 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">2. Realistic Answer Submission</h3>
              <p className="text-white/70">Users can now fill in their answers in generated text boxes, simulating the test-taking experience.</p>
            </div>
            {/* Step 3 */}
            <div className="bg-[#18181B] rounded-2xl p-8 flex flex-col items-center text-center border border-white/5">
              <div className="w-14 h-14 flex items-center justify-center rounded-full bg-[#FFBE1A]/10 mb-4">
                <svg className="w-8 h-8 text-[#FFBE1A]" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">3. AutoMark & Get Detailed Explanation</h3>
              <p className="text-white/70">Receive detailed marks and explanations for every question—so you can focus on encouragement, not correction.</p>
            </div>
          </div>
          <div className="mt-12 w-full">
            <div style={{ position: 'relative', paddingBottom: '56.25%', height: 0, overflow: 'hidden' }}>
              <iframe
                src="https://www.youtube.com/embed/4ry9Hppynwo?mute=1"
                title="YouTube video player"
                frameBorder="0"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                referrerPolicy="strict-origin-when-cross-origin"
                allowFullScreen
                style={{ position: 'absolute', top: 0, left: 0, width: '100%', height: '100%' }}
              ></iframe>
            </div>
          </div>
          {/*
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Benefit 1 }
            <div className="bg-[#18181B] rounded-2xl p-8 flex flex-col items-center text-center border border-white/5">
              <div className="w-12 h-12 flex items-center justify-center rounded-full bg-[#4ADE80]/10 mb-4">
                <svg className="w-7 h-7 text-[#4ADE80]" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V4a2 2 0 10-4 0v1.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                </svg>
              </div>
              <h4 className="text-lg font-semibold text-white mb-2">Cut Marking Time by 90%</h4>
              <p className="text-white/70">Automate grading in just 3 clicks and reclaim precious family evenings.</p>
            </div>
            {/* Benefit 2 }
            <div className="bg-[#18181B] rounded-2xl p-8 flex flex-col items-center text-center border border-white/5">
              <div className="w-12 h-12 flex items-center justify-center rounded-full bg-[#4ADE80]/10 mb-4">
                <svg className="w-7 h-7 text-[#4ADE80]" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M12 8c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4zm0 0V4m0 16v-4" />
                </svg>
              </div>
              <h4 className="text-lg font-semibold text-white mb-2">Reframe Your Evenings</h4>
              <p className="text-white/70">From marking to making memories—enjoy more quality time with your child.</p>
            </div>
          </div>
          */}
          <div className="mt-12 flex flex-col items-center">
            <div className="inline-flex items-center bg-white/5 px-4 py-2 rounded-full mb-4">
              <span className="text-white/70 text-sm">
                Try It Free — We’ll Mark Your First 5 Papers on Us Every Month
              </span>
            </div>
            <div className="text-white/60 text-center text-lg">
              Join <span className="text-[#FFBE1A] font-semibold">547+ Singaporean Parents</span> saving <span className="text-[#FFBE1A] font-semibold">10 hours weekly</span>.<br />
              Experience why parents trust our platform to simplify revision marking.
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      {/* Launch Announcement Video */}
      

      {/* Pricing Section */}
      <section id="pricing" className="py-20 px-4 bg-[#18181B]">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-4xl font-bold text-white text-center mb-4">
            Simple, Family-Friendly Pricing
          </h2>
          <div className="text-center text-white/70 mb-10">
            Try it free — your first 5 papers are on us every month. No credit card required.
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-7xl mx-auto">
            {pricingPlans.map((plan, index) => (
              <PricingCard key={index} {...plan} />
            ))}
          </div>
        </div>
      </section>

      {/* Contact Form Section */}
      <section id="contact" className="py-20 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4">
              Get in Touch
            </h2>
            <p className="text-lg text-white/60 max-w-2xl mx-auto">
              Have questions about our product? Need help getting started? We're here to help.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start">
            {/* Contact Information */}
            <div className="space-y-8">
              <div>
                <h3 className="text-xl font-semibold text-white mb-4">Contact Information</h3>
                <p className="text-white/60 mb-6">
                  Fill out the form and we'll get back to you within 24 hours.
                </p>
              </div>

              <div className="space-y-6">
                <div className="flex items-start space-x-4">
                  <div className="p-3 bg-white/5 rounded-lg">
                    <EnvelopeIcon className="w-6 h-6 text-[#FFBE1A]" />
                  </div>
                  <div>
                    <h4 className="text-white font-medium mb-1">Email</h4>
                    <a href="mailto:<EMAIL>" className="text-white/60 hover:text-[#FFBE1A]">
                      <EMAIL>
                    </a>
                  </div>
                </div>


                <div className="flex items-start space-x-4">
                  <div className="p-3 bg-white/5 rounded-lg">
                    <MapPinIcon className="w-6 h-6 text-[#FFBE1A]" />
                  </div>
                  <div>
                    <h4 className="text-white font-medium mb-1">Office</h4>
                    <p className="text-white/60">
                    109 North Bridge Road<br />
                    Singapore 179097
                    </p>
                  </div>
                </div>
              </div>

            </div>

            {/* Contact Form */}
            <div className="bg-white/[0.02] rounded-2xl p-8 border border-white/5">
              <form
                className="space-y-6"
                onSubmit={async (e) => {
                  e.preventDefault();
                  setSubmissionMessage(''); // Clear previous messages
                  const form = e.currentTarget;
                  const formData = {
                    firstName: form.first_name.value.trim(),
                    lastName: form.last_name.value.trim(),
                    email: form.email.value.trim(),
                    subject: form.subject.value.trim(),
                    message: form.message.value.trim(),
                  };

                  if (!formData.firstName || !formData.email || !formData.message) {
                    setSubmissionMessage('Please fill in the required fields: First Name, Email, and Message.');
                    return;
                  }

                  try {
                    const response = await fetch('/api/email/contact', {
                      method: 'POST',
                      headers: {
                        'Content-Type': 'application/json',
                      },
                      body: JSON.stringify(formData),
                    });

                    const result = await response.json();

                    if (response.ok && result.success) {
                      setSubmissionMessage('Message sent successfully!');
                      form.reset();
                    } else {
                      setSubmissionMessage('Failed to send message: ' + (result.error || 'Unknown error'));
                    }
                  } catch (error) {
                    setSubmissionMessage('An error occurred while sending the message.');
                  }
                }}
              >
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="first_name" className="block text-sm font-medium text-white mb-2">
                      First Name <span className="text-[#FFBE1A]">*</span>
                    </label>
                    <input
                      type="text"
                      id="first_name"
                      name="first_name"
                      required
                      className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FFBE1A]/50 focus:border-[#FFBE1A] text-white placeholder-white/40"
                      placeholder="John"
                    />
                  </div>
                  <div>
                    <label htmlFor="last_name" className="block text-sm font-medium text-white mb-2">
                      Last Name
                    </label>
                    <input
                      type="text"
                      id="last_name"
                      name="last_name"
                      className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FFBE1A]/50 focus:border-[#FFBE1A] text-white placeholder-white/40"
                      placeholder="Doe"
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-white mb-2">
                    Email <span className="text-[#FFBE1A]">*</span>
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    required
                    className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FFBE1A]/50 focus:border-[#FFBE1A] text-white placeholder-white/40"
                    placeholder="<EMAIL>"
                  />
                </div>

                <div>
                  <label htmlFor="subject" className="block text-sm font-medium text-white mb-2">
                    Subject
                  </label>
                  <input
                    type="text"
                    id="subject"
                    name="subject"
                    className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FFBE1A]/50 focus:border-[#FFBE1A] text-white placeholder-white/40"
                    placeholder="How can we help?"
                  />
                </div>

                <div>
                  <label htmlFor="message" className="block text-sm font-medium text-white mb-2">
                    Message <span className="text-[#FFBE1A]">*</span>
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    rows={4}
                    required
                    className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FFBE1A]/50 focus:border-[#FFBE1A] text-white placeholder-white/40"
                    placeholder="Your message..."
                  ></textarea>
                </div>

                <button
                  type="submit"
                  className="w-full px-6 py-3 bg-[#FFBE1A] text-black font-medium rounded-lg hover:bg-[#FFBE1A]/90 transition-colors"
                >
                  Send Message
                </button>
                {submissionMessage && (
                  <p className="mt-4 text-center text-sm font-medium text-[#FFBE1A]">
                    {submissionMessage}
                  </p>
                )}
              </form>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section id="faq" className="py-20 px-4 bg-[#18181B]">
        <div className="max-w-3xl mx-auto">
          <h2 className="text-4xl font-bold text-white text-center mb-12">
            Frequently Asked Questions
          </h2>
          <div className="space-y-4">
            {faqs.map((faq, index) => (
              <FaqItem key={index} {...faq} />
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-24 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">
              What Parents Say
            </h2>
            <div className="text-white/70 text-lg">
              Join <span className="text-[#FFBE1A] font-semibold">547+ families</span> who have reclaimed their time and peace of mind.
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {testimonials.map((testimonial, index) => (
              <TestimonialCard key={index} {...testimonial} />
            ))}
          </div>
        </div>
      </section>
      </main>

      {/* Footer */}
      <footer className="py-20 px-4 border-t border-white/5 bg-[#121212]">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            <div>
              <h3 className="font-semibold text-white mb-4">Navigation</h3>
              <ul className="space-y-2">
                <li><Link href="#home" className="text-white/70 hover:text-white">Home</Link></li>
                <li><Link href="#why" className="text-white/70 hover:text-white">Why</Link></li>
                <li><Link href="#how" className="text-white/70 hover:text-white">How It Works</Link></li>
                <li><Link href="#pricing" className="text-white/70 hover:text-white">Pricing</Link></li>
                <li><Link href="#faq" className="text-white/70 hover:text-white">FAQ</Link></li>
                              </ul>
            </div>
            <div>
              <h3 className="font-semibold text-white mb-4">Legal</h3>
              <ul className="space-y-2">
                <li><Link href="/terms" className="text-white/70 hover:text-white">Terms & Conditions</Link></li>
                <li><Link href="/privacy" className="text-white/70 hover:text-white">Privacy</Link></li>
                <li><Link href="#contact" className="text-white/70 hover:text-white">Contact</Link></li>
              </ul>
            </div>
          </div>
          <div className="text-center text-white/60 mt-12">
            &copy; 2025 TestPaperHero. All rights reserved.
          </div>
        </div>
      </footer>
    </>
  )
}

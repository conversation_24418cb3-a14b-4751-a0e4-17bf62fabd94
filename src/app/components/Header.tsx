'use client'

import Image from 'next/image'
import Link from 'next/link'
import Script from 'next/script'

export default function Header() {
  return (
    <nav className="fixed top-0 w-full z-50 bg-[#0A0A0A]/80 backdrop-blur-sm border-b border-white/5">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <div className="flex items-center">
            <Link href="/" className="text-xl font-bold text-white flex items-center space-x-2">
              <Image src="/logo.svg" alt="Logo" width={32} height={32} priority />
              <span>TestPaperHero</span>
            </Link>
          </div>
          <div className="hidden md:flex items-center space-x-8">
            <Link href="/#home" className="text-sm text-white/70 hover:text-white">Home</Link>
            <Link href="/#why" className="text-sm text-white/70 hover:text-white">Why</Link>
            <Link href="/#how" className="text-sm text-white/70 hover:text-white">How It Works</Link>
            <Link href="/#pricing" className="text-sm text-white/70 hover:text-white">Pricing</Link>
            <Link href="/#faq" className="text-sm text-white/70 hover:text-white">FAQ</Link>
            <Link href="/#contact" className="text-sm text-white/70 hover:text-white">Contact</Link>
          </div>
          <div className="flex items-center space-x-2 sm:space-x-4">
            <a
              href="/auth"
              className="text-xs sm:text-sm text-white/70 hover:text-white"
            >
              Sign in
            </a>
            <a
              href="/auth"
              className="bg-[#FFBE1A] text-black text-xs sm:text-sm px-2 py-1 sm:px-4 sm:py-2 rounded-lg hover:bg-[#FFBE1A]/90"
            >
              Try It Free
            </a>
          </div>
        </div>
      </div>
    </nav>
  )
}

import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export const dynamic = 'force-dynamic'

export async function GET(request: NextRequest) {
  const supabase = createRouteHandlerClient({ cookies })
  const requestUrl = new URL(request.url)

  try {
    // Sign out from Supabase
    const { error } = await supabase.auth.signOut()

    if (error) {
      console.error('Error signing out:', error)
      throw error
    }

    // supabase.auth.signOut() handles cookie invalidation server-side.
    // Manual deletion here is usually redundant and was causing type errors.
    // If specific cookies needed deletion, it would be done on the NextResponse object.

    // Redirect to the auth page after signout
    return NextResponse.redirect(`${requestUrl.origin}/auth`, {
      status: 302,
    })
  } catch (error) {
    console.error('Manual signout failed:', error)
    // Redirect to auth page with an error message
    const redirectUrl = new URL('/auth', requestUrl.origin)
    redirectUrl.searchParams.set('error', 'Could not sign out. Please try again.')
    return NextResponse.redirect(redirectUrl.toString(), {
      status: 302, // Use 302 for temporary redirect
    })
  }
}

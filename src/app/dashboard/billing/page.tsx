'use client'

import { useState, useEffect, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { loadStripe } from '@stripe/stripe-js'
import PricingCard from '@/app/components/PricingCard'
import {
  CreditCardIcon,
  ClockIcon,
  CheckIcon,
} from '@heroicons/react/24/outline'

// Initialize Stripe
const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!)

const pricingPlans = [
  {
    title: "Free",
    price: "0",
    period: "/month",
    description: "Mark up to 5 papers / mo.\nAI explanations for every answer\nNo credit card required",
    features: [
      "Mark up to 5 papers / mo.",
      "AI explanations for every answer",
      "No credit card required"
    ],
    buttonText: "",
    priceId: ""
  },
  {
    title: "Starter Monthly",
    price: "19",
    period: "/month",
    description: "Stimulate up to 50 papers / mo. Ideal for steady progress with priority marking and reliable email support. Perfect for core subjects English, Math & Science.",
    features: [
      "Stimulate up to 50 papers / mo.",
      "Covers English, Math & Science",
      "Priority marking speed",
      "Email support",
      "Cancel anytime"
    ],
    buttonText: "Choose Starter Plan",
    popular: true,
    priceId: process.env.NEXT_PUBLIC_STRIPE_PRICE_PRO
  },
  {
    title: "Tutors Monthly Plan",
    price: "59",
    period: "/month",
    description: "Stimulate up to 300 papers / mo. Designed for tuition centers to efficiently manage and stimulate large volumes of papers with advanced features and dedicated support.",
    features: [
      "Stimulate up to 300 papers / mo.",
      "Highest priority marking speed",
      "Best value for tuition centers",
      "Email support",
      "Cancel anytime"
    ],
    buttonText: "Choose Tutors Plan",
    priceId: process.env.NEXT_PUBLIC_STRIPE_PRICE_ENTERPRISE
  }
]

interface Subscription {
  id: string
  subscription_id: string
  price_id: string
  status: string
  stripe_portal_url: string
}

export default function BillingPage() {
  const [currentSubscription, setCurrentSubscription] = useState<Subscription | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [user, setUser] = useState<any>(null)
  const [credits, setCredits] = useState<number | null>(null)
  const [isPortalLoading, setIsPortalLoading] = useState(false); // Add loading state for portal button
  const supabase = createClientComponentClient()
  const router = useRouter()

  const fetchBillingData = useCallback(
    async (userId: string) => {
      try {
        // Fetch latest subscription
        const { data: subscription, error: subError } = await supabase
          .from('customer_subscriptions')
          .select('*')
          .eq('user_id', userId)
          .order('created', { ascending: false })
          .limit(1)
          .maybeSingle()

        if (subError) {
          console.error('Error fetching subscription:', subError)
          setError('Error loading subscription data')
          return
        }

        // Fetch user credits
        const { data: profileData, error: profileError } = await supabase
          .from('profiles')
          .select('credits')
          .eq('id', userId)
          .single()

        if (profileError) {
          console.error('Error fetching credits:', profileError)
        } else {
          setCredits(profileData?.credits ?? null)
        }

        if (subscription) {
          setCurrentSubscription(subscription)
        } else {
          setCurrentSubscription(null)
        }
      } catch (err) {
        console.error('Error fetching billing data:', err)
        setError('Failed to load billing information')
      }
    },
    [supabase]
  )

  const checkUser = useCallback(async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) {
        router.push('/auth')
        return
      }
      setUser(user)
      await fetchBillingData(user.id)
    } catch (error) {
      console.error('Error checking user:', error)
      setError('Authentication error occurred')
    } finally {
      setIsLoading(false)
    }
  }, [supabase.auth, router, fetchBillingData])

  useEffect(() => {
    let didCancel = false
    let retryCount = 0
    const maxRetries = 3
    const retryDelay = 3000 // 3 seconds

    const checkUserWithTimeoutAndRetry = async () => {
      setIsLoading(true)
      setError(null)
      while (retryCount < maxRetries && !didCancel) {
        try {
          await Promise.race([
            checkUser(),
            new Promise((_, reject) =>
              setTimeout(() => reject(new Error('timeout')), 10000)
            )
          ])
          // Success, break out of loop
          break
        } catch (err) {
          retryCount++
          if (retryCount >= maxRetries) {
            if (!didCancel) {
              // Last attempt failed, reload the page
              window.location.reload()
            }
            return
          }
          // Wait before retrying
          await new Promise(res => setTimeout(res, retryDelay))
        }
      }
      if (!didCancel) setIsLoading(false)
    }

    checkUserWithTimeoutAndRetry()

    return () => {
      didCancel = true
    }
  }, [checkUser])

  // Function to create and redirect to Stripe Customer Portal session
  const handleManageSubscription = async () => {
    setIsPortalLoading(true);
    setError(null);
    try {
      const response = await fetch('/api/create-customer-portal-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        // No body needed for this API route based on its implementation
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to create customer portal session');
      }

      // Redirect to Stripe Customer Portal URL
      window.location.href = result.url;
    } catch (err) {
      console.error('Error creating customer portal session:', err);
      setError(err instanceof Error ? err.message : 'Failed to open billing portal');
    } finally {
      setIsPortalLoading(false);
    }
  };

  async function handleSubscribe(priceId: string) {
    try {
      setError(null)
      
      if (!user) {
        setError('Please log in to continue')
        return
      }

      // Use priceId directly without splitting by comma
      const response = await fetch('/api/create-checkout-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          priceId,
        }),
      })

      const result = await response.json()
      
      if (!response.ok) {
        throw new Error(result.error || 'Failed to create checkout session')
      }

      // Redirect to Stripe Checkout
      window.location.href = result.url
    } catch (err) {
      console.error('Error:', err)
      setError(err instanceof Error ? err.message : 'Failed to process subscription')
    }
  }


  if (isLoading) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-white">Loading...</div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="max-w-7xl mx-auto p-4">
        <div className="bg-red-500/10 text-red-500 p-4 rounded-lg">
          Please log in to access billing information
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-7xl mx-auto space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-white">Billing & Subscription</h1>
        <p className="mt-2 text-white/60">
          Manage your subscription and billing information
        </p>
      </div>

      {error && (
        <div className="bg-red-500/10 text-red-500 p-4 rounded-lg">
          {error}
        </div>
      )}

      {/* Current Plan */}
      <div className="bg-[#111111] rounded-2xl p-8 border border-white/5">
        <h2 className="text-xl font-semibold text-white mb-6">Current Plan</h2>
        {currentSubscription ? (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-medium text-white">
                  {pricingPlans.find(plan => plan.priceId === currentSubscription.price_id)?.title || 'Unknown Plan'}
                </h3>
              </div>
              <span className={`px-3 py-1 rounded-full text-sm ${
              currentSubscription.status === 'active' 
                ? 'bg-green-500/10 text-green-500'
                : currentSubscription.status === 'canceled'
                ? 'bg-yellow-500/10 text-yellow-500'
                : 'bg-red-500/10 text-red-500'
            }`}>
              {currentSubscription.status}
            </span>
            </div>
            {process.env.NEXT_PUBLIC_SHOW_CREDITS !== "false" && typeof credits === 'number' && (
              <div className="mt-4">
                <div className="flex items-center space-x-2">
                  <span className="text-white/60">Credits:</span>
                  <span className="px-2 py-1 rounded bg-blue-900 text-blue-200 text-sm font-semibold">
                    {credits}
                  </span>
                </div>
              </div>
            )}

            {/* Replace static link with button calling handleManageSubscription */}
            <button
              onClick={handleManageSubscription}
              disabled={isPortalLoading}
              className="mt-4 inline-block px-4 py-2 bg-white/10 hover:bg-white/20 text-white rounded-lg transition-colors disabled:opacity-50"
            >
              {isPortalLoading ? 'Opening Portal...' : 'Manage in Stripe'}
            </button>
          </div>
        ) : (
          <p className="text-white/60">No active subscription</p>
        )}
      </div>

      {/* Available Plans */}
      <div className="bg-[#111111] rounded-2xl p-8 border border-white/5">
        <h2 className="text-xl font-semibold text-white mb-6">Available Plans</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {pricingPlans.map((plan, index) => {
            // Do not show subscription button for Free plan
            if (plan.title === "Free") {
              return <PricingCard key={index} {...{ ...plan, priceId: plan.priceId ?? "", buttonText: "" }} />;
            }
            return <PricingCard key={index} {...{ ...plan, priceId: plan.priceId ?? "" }} />;
          })}
        </div>
      </div>

    </div>
  )
}

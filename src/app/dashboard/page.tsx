'use client'

import { useState, useEffect } from 'react'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import Chat from '@/components/dashboard/Chat'

export default function Dashboard() {
  const [profile, setProfile] = useState<any>(null)
  const supabase = createClientComponentClient()

  useEffect(() => {
    fetchProfile()
  }, [fetchProfile])

  async function fetchProfile() {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (user) {
        const { data } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single()
        
        if (data) setProfile(data)
      }
    } catch (error) {
      console.error('Error fetching profile:', error)
    }
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-white">Overview</h1>
        <p className="mt-1 text-white/60">
          Welcome to your dashboard
        </p>
      </div>

      {/* Main Content */}
      <div className="flex flex-col gap-8">
        {/* Welcome Section */}
        <div className="bg-[#111111] border border-white/5 rounded-xl p-6">
          <h2 className="text-xl font-semibold text-white mb-4">
            Getting Started
          </h2>
          <p className="text-white/60 mb-4">
            Welcome to your SaaS dashboard. Here you can easily manage your account, subscriptions, and explore the powerful features of our AI-powered test paper marking platform. Use the quick links below to quickly navigate to essential sections and get the most out of your experience.
          </p>
          <ul className="space-y-2 text-white/60">
            <li className="flex items-center">
              <span className="w-2 h-2 bg-green-500 rounded-full mr-2" />
              Customize your profile to personalize your experience
            </li>
            <li className="flex items-center">
              <span className="w-2 h-2 bg-green-500 rounded-full mr-2" />
              Manage your subscription plans and billing details
            </li>
            <li className="flex items-center">
              <span className="w-2 h-2 bg-green-500 rounded-full mr-2" />
              Explore features, integrations, and start marking papers
            </li>
          </ul>
        </div>

        {/* Quick Links */}
        <div className="bg-[#111111] border border-white/5 rounded-xl p-6">
          <h2 className="text-xl font-semibold text-white mb-4">
            Quick Links
          </h2>
          <div className="grid grid-cols-2 gap-4">
            <button
              onClick={() => window.location.href = '/dashboard/billing'}
              className="p-4 bg-white/5 rounded-lg hover:bg-white/10 transition-colors text-left"
            >
              <h3 className="font-medium text-white">Billing & Subscriptions</h3>
              <p className="text-sm text-white/60">Manage your subscription plans</p>
            </button>
            <button
              onClick={() => window.location.href = '/dashboard/profile'}
              className="p-4 bg-white/5 rounded-lg hover:bg-white/10 transition-colors text-left"
            >
              <h3 className="font-medium text-white">Profile</h3>
              <p className="text-sm text-white/60">Manage your account</p>
            </button>
            <button
              onClick={() => window.location.href = '/dashboard/smartmark'}
              className="p-4 bg-white/5 rounded-lg hover:bg-white/10 transition-colors text-left"
            >
              <h3 className="font-medium text-white">Smart Mark</h3>
              <p className="text-sm text-white/60">AI-powered smart marking tool</p>
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

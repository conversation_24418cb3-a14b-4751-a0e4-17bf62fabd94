"use client";

import { useState, useEffect } from "react";
import styles from "../../../styles/Home.module.css";

import 'react-pdf/dist/esm/Page/TextLayer.css';
import 'react-pdf/dist/esm/Page/AnnotationLayer.css';

import dynamic from "next/dynamic";

// Dynamically import Document and Page to avoid SSR issues (as in MVP)
const Document = dynamic(() => import("react-pdf").then(mod => mod.Document), { ssr: false });
const Page = dynamic(() => import("react-pdf").then(mod => mod.Page), { ssr: false });

import { useCredits } from '@/components/dashboard/CreditsContext'
import LockedPageGuard from '@/components/dashboard/LockedPageGuard'
import Timer from '@/components/dashboard/Timer'; // Import Timer component

// --- Dropzone component for PDF upload ---
import Image from "next/image";
import { useCallback } from "react";

function Dropzone({ onFileAccepted, errorMessage }: { onFileAccepted: (e: any) => void, errorMessage?: string }) {
  const handleDrop = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    const file = e.dataTransfer.files?.[0];
    if (file) {
      // Wrap in a fake event to reuse handleFileChange
      onFileAccepted({ target: { files: [file] } });
    }
  }, [onFileAccepted]);

  const handleClick = useCallback(() => {
    const input = document.createElement("input");
    input.type = "file";
    input.accept = "application/pdf";
    input.onchange = onFileAccepted;
    input.click();
  }, [onFileAccepted]);

  return (
    <div
      onDrop={handleDrop}
      onDragOver={e => { e.preventDefault(); e.stopPropagation(); }}
      onClick={handleClick}
      style={{
        border: "2.5px dashed #1976d2",
        borderRadius: 18,
        background: "#fff",
        minHeight: 220,
        minWidth: 320,
        maxWidth: 420,
        margin: "40px auto 0 auto",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        cursor: "pointer",
        boxShadow: "0 2px 12px 0 rgba(60,72,88,0.07)",
        transition: "border 0.2s",
        padding: "32px 24px 24px 24px",
        textAlign: "center"
      }}
      tabIndex={0}
      role="button"
      aria-label="Upload PDF"
    >
      <Image src="/file.svg" alt="PDF" width={56} height={56} style={{ marginBottom: 18, opacity: 0.85 }} />
      <div style={{ fontWeight: 600, fontSize: 20, color: "#1976d2", marginBottom: 8 }}>
        Drag and drop PDF here<br />or <span style={{ textDecoration: "underline", color: "#1565c0" }}>click to upload</span>
      </div>
      <div style={{ color: "#888", fontSize: 15, marginBottom: 6 }}>
        Max file size: 10MB
      </div>
      {errorMessage && (
        <div style={{ color: "#e53935", fontWeight: 600, marginTop: 8, fontSize: 15 }}>
          {errorMessage}
        </div>
      )}
    </div>
  );
}

export default function AutoMarkingMVPPage() {
  // Credits
  const { credits, refreshCredits } = useCredits()
  const [deducting, setDeducting] = useState(false)
  const [creditError, setCreditError] = useState<string | null>(null)
  const [creditSuccess, setCreditSuccess] = useState<string | null>(null)
  // Local flag to bypass LockedPageGuard after marking
  const [justMarked, setJustMarked] = useState(false)
  const [testDuration, setTestDuration] = useState(120); // State for test duration in minutes

  // State
  const [file, setFile] = useState<any>(null);
  const [pdfUrl, setPdfUrl] = useState<string | null>(null);
  const [dropzoneError, setDropzoneError] = useState<string | null>(null);
  const [answers, setAnswers] = useState<any>(() => {
    if (typeof window !== "undefined") {
      const saved = localStorage.getItem("pdfAutoMarkingAnswers");
      return saved ? JSON.parse(saved) : {};
    }
    return {};
  });
  const [markingResults, setMarkingResults] = useState<any>(() => {
    if (typeof window !== "undefined") {
      const saved = localStorage.getItem("pdfAutoMarkingResults");
      return saved ? JSON.parse(saved) : null;
    }
    return null;
  });
  const [loading, setLoading] = useState(false);
  const [questionCount, setQuestionCount] = useState<number>(() => {
    if (typeof window !== "undefined") {
      const saved = localStorage.getItem("pdfAutoMarkingAnswerKeys");
      return saved ? JSON.parse(saved).length : 0;
    }
    return 0;
  });
  const [fileObject, setFileObject] = useState<any>(null);
  const [localPdfUrl, setLocalPdfUrl] = useState<string | null>(null);
  const [geminiRaw, setGeminiRaw] = useState("");
  const [r2Key, setR2Key] = useState<any>(null);
  const [answerKeys, setAnswerKeys] = useState<any>(() => {
    if (typeof window !== "undefined") {
      const saved = localStorage.getItem("pdfAutoMarkingAnswerKeys");
      if (saved) {
        try {
          const parsed = JSON.parse(saved);
          return Array.isArray(parsed) ? parsed : [];
        } catch {
          return [];
        }
      }
      return [];
    }
    return [];
  });
  const [answerSheet, setAnswerSheet] = useState<any>(() => {
    if (typeof window !== "undefined") {
      const saved = localStorage.getItem("pdfAutoMarkingAnswerSheet");
      return saved ? JSON.parse(saved) : {};
    }
    return {};
  });
  const [questions, setQuestions] = useState<any>(() => {
    if (typeof window !== "undefined") {
      const saved = localStorage.getItem("pdfAutoMarkingQuestions");
      return saved ? JSON.parse(saved) : {};
    }
    return {};
  });
  const [resultsFontSize, setResultsFontSize] = useState(16);
  const [regenLoading, setRegenLoading] = useState<any>({});
  const [askAIState, setAskAIState] = useState<any>({});
  const [scale, setScale] = useState(1.2);
  const [numPages, setNumPages] = useState<number | null>(null);

  // Set pdf.js workerSrc on client side (use local worker + cache busting)
  useEffect(() => {
    import("react-pdf").then((mod) => {
      // Add timestamp for cache busting
      mod.pdfjs.GlobalWorkerOptions.workerSrc = `/pdf.worker.min.js?t=${Date.now()}`;
    });
  }, []);

  // PDF Zoom controls
  function handleZoomIn() {
    setScale((prev) => Math.min(prev + 0.2, 3));
  }
  function handleZoomOut() {
    setScale((prev) => Math.max(prev - 0.2, 0.5));
  }
  function handleZoomReset() {
    setScale(1.2);
  }

  function onDocumentLoadSuccess({ numPages }: { numPages: number }) {
    setNumPages(numPages);
  }

  function onDocumentLoadError(error: Error) {
    console.error('react-pdf Document Error:', error);
  }

  function handleFileChange(event: any) {
    // Accept both input and drop events
    const uploadedFile = event?.target?.files?.[0];
    if (!uploadedFile) return;
    // Validate file type
    if (uploadedFile.type !== "application/pdf") {
      setDropzoneError("Only PDF files are allowed.");
      return;
    }
    // Validate file size (<= 10MB)
    if (uploadedFile.size > 10 * 1024 * 1024) {
      setDropzoneError("File size exceeds 10MB limit.");
      return;
    }
    setDropzoneError(null);
    fileToBase64(uploadedFile).then((base64) => {
      if (typeof window !== "undefined") {
        localStorage.setItem("pdfAutoMarkingFile", base64 as string);
      }
    });
    setFile(uploadedFile);
    setFileObject(uploadedFile);
    setLocalPdfUrl(URL.createObjectURL(uploadedFile));
    setPdfUrl(null);
    setMarkingResults(null);
    setAnswers({});
    setQuestionCount(0);
    setAnswerKeys([]);
    setAnswerSheet({});
    setQuestions({});
    setGeminiRaw("");
    if (typeof window !== "undefined") {
      localStorage.removeItem("pdfAutoMarkingAnswers");
      localStorage.removeItem("pdfAutoMarkingResults");
      localStorage.removeItem("pdfAutoMarkingFile");
      localStorage.removeItem("pdfAutoMarkingAnswerKeys");
      localStorage.removeItem("pdfAutoMarkingAnswerSheet");
      localStorage.removeItem("pdfAutoMarkingQuestions");
      localStorage.removeItem("pdfAutoMarkingR2Key"); // Also remove r2Key on new file upload
    }
  }

  function handleAnswerChange(questionNumber: string, value: string) {
    setAnswers((prev: any) => {
      const updated = { ...prev, [questionNumber]: value };
      if (typeof window !== "undefined") {
        localStorage.setItem("pdfAutoMarkingAnswers", JSON.stringify(updated));
      }
      return updated;
    });
  }

  async function handleStartRendering() {
    if (!fileObject) return;
    setLoading(true);
    try {
      // 1. Request a pre-signed URL from the API
      const presignRes = await fetch("/api/r2-presign-upload", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ filename: fileObject.name, contentType: fileObject.type }),
      });
      if (!presignRes.ok) throw new Error("Failed to get R2 presigned URL");
      const { url, key } = await presignRes.json();

      // 2. Upload the PDF directly to R2
      const uploadRes = await fetch(url, {
        method: "PUT",
        headers: { "Content-Type": fileObject.type || "application/pdf" },
        body: fileObject,
      });
      if (!uploadRes.ok) throw new Error("Failed to upload PDF to R2 storage");

      // 3. Set the public R2 URL for PDF viewing (optional, not used in viewer)
      setR2Key(key);
      if (typeof window !== "undefined") {
        localStorage.setItem("pdfAutoMarkingR2Key", key); // Save r2Key to localStorage
      }

      // 4. Call API to extract answer keys and answer sheet, passing the R2 key
      const res = await fetch("/api/extract-questions", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ r2Key: key }),
      });

      const data = await res.json();
      if (Array.isArray(data.keys) && data.keys.length > 0) {
        setAnswerKeys(data.keys);
        setAnswerSheet(data.answers || {});
        setQuestions(data.questions || {});
        setQuestionCount(data.keys.length);
        if (typeof window !== "undefined") {
          localStorage.setItem("pdfAutoMarkingAnswerKeys", JSON.stringify(data.keys));
          localStorage.setItem("pdfAutoMarkingAnswerSheet", JSON.stringify(data.answers || {}));
          localStorage.setItem("pdfAutoMarkingQuestions", JSON.stringify(data.questions || {}));
        }
      } else {
        setAnswerKeys([]);
        setAnswerSheet({});
        setQuestions({});
        setQuestionCount(0);
        if (typeof window !== "undefined") {
          localStorage.removeItem("pdfAutoMarkingAnswerKeys");
          localStorage.removeItem("pdfAutoMarkingAnswerSheet");
          localStorage.removeItem("pdfAutoMarkingQuestions");
        }
      }
      setGeminiRaw(data.geminiRaw || "");
    } catch (error) {
      setAnswerKeys([]);
      setAnswerSheet({});
      setQuestionCount(0);
      if (typeof window !== "undefined") {
        localStorage.removeItem("pdfAutoMarkingAnswerKeys");
        localStorage.removeItem("pdfAutoMarkingAnswerSheet");
        localStorage.removeItem("pdfAutoMarkingQuestions");
      }
    }
    setLoading(false);
  }

  // Deduct 1 credit (copied from page1)
  const handleDeduct = async () => {
    setDeducting(true)
    setCreditError(null)
    setCreditSuccess(null)
    try {
      const res = await fetch('/api/credits/deduct', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ amount: 1 }),
      })
      const result = await res.json()
      if (result.success) {
        setCreditSuccess('Credit deducted!')
        // DO NOT call refreshCredits here; delay until after results are shown
        return true
      } else {
        setCreditError(result.error || 'Failed to deduct credit')
        return false
      }
    } catch (err) {
      setCreditError('Unexpected error deducting credit')
      return false
    } finally {
      setDeducting(false)
    }
  }

  async function handleReveal() {
    setCreditError(null)
    setCreditSuccess(null)
    setLoading(true);
    // First, try to deduct credit
    const deducted = await handleDeduct();
    if (!deducted) {
      setLoading(false);
      return;
    }
    try {
      // Set sessionStorage flag to bypass LockedPageGuard for results
      if (typeof window !== "undefined") {
        window.sessionStorage.setItem('smartmarkJustMarked', 'true');
      }
      const res = await fetch("/api/mark", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ r2Key, answers, answerSheet, questions }),
      });

      const data = await res.json();
      setMarkingResults(data);
      setJustMarked(true); // Set local flag for UI
      if (typeof window !== "undefined") {
        localStorage.setItem("pdfAutoMarkingResults", JSON.stringify(data));
      }
    } catch (error) {
      setMarkingResults({ error: "Failed to mark answers." });
    }
    setLoading(false);
  }

  function handleReset() {
    if (window.confirm("Are you sure you want to reset? This will reload the entire page.")) {
      if (typeof window !== "undefined") {
        localStorage.removeItem("pdfAutoMarkingAnswers");
        localStorage.removeItem("pdfAutoMarkingResults");
        localStorage.removeItem("pdfAutoMarkingFile");
        localStorage.removeItem("pdfAutoMarkingAnswerKeys");
        localStorage.removeItem("pdfAutoMarkingAnswerSheet");
        localStorage.removeItem("pdfAutoMarkingQuestions");
        localStorage.removeItem("pdfAutoMarkingR2Key"); // Remove r2Key from localStorage on reset
      }
      // Refresh credits after leaving results page
      refreshCredits();
      window.location.reload();
    }
  }

  function fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => resolve((reader.result as string).split(",")[1]);
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  }

  async function handleRegenerateExplanation(key: string) {
    setRegenLoading((prev: any) => ({ ...prev, [key]: true }));
    try {
      const userAnswer = answers[key] || "";
      const correctAnswer = answerSheet[key] || "";
      const questionText = questions[key] || "";
      const res = await fetch("/api/regenerate-explanation", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          r2Key,
          questionKey: key,
          userAnswer,
          correctAnswer,
          questionText,
        }),
      });
      const data = await res.json();
      if (data && data.explanation) {
        setMarkingResults((prev: any) => ({
          ...prev,
          result: {
            ...prev.result,
            [key]: {
              ...prev.result[key],
              explanation: data.explanation,
              correct: typeof data.correct === "boolean" ? data.correct : prev.result[key]?.correct,
            }
          }
        }));
      }
    } catch (error) {
      setMarkingResults((prev: any) => ({
        ...prev,
        result: {
          ...prev.result,
          [key]: {
            ...prev.result[key],
            explanation: "Failed to regenerate explanation.",
          }
        }
      }));
    }
    setRegenLoading((prev: any) => ({ ...prev, [key]: false }));
  }

  async function handleRegenerateAllExplanations() {
    if (!answerKeys || answerKeys.length === 0) return;
    setRegenLoading((prev: any) => {
      const loadingState = { ...prev };
      answerKeys.forEach((key: string) => { loadingState[key] = true; });
      return loadingState;
    });
    try {
      const res = await fetch("/api/mark", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ r2Key, answers, answerSheet, questions }),
      });
      const data = await res.json();
      setMarkingResults(data);
      if (typeof window !== "undefined") {
        localStorage.setItem("pdfAutoMarkingResults", JSON.stringify(data));
      }
    } catch (error) {
      setMarkingResults((prev: any) => ({
        ...prev,
        error: "Failed to re-mark answers.",
      }));
    }
    setRegenLoading({});
  }

  // Restore PDF and state from localStorage on mount
  useEffect(() => {
    if (typeof window !== "undefined") {
      const base64 = localStorage.getItem("pdfAutoMarkingFile");
      const savedR2Key = localStorage.getItem("pdfAutoMarkingR2Key"); // Retrieve r2Key
      if (base64 && !file && !fileObject) {
        // Reconstruct File object from base64
        const byteString = atob(base64);
        const ab = new ArrayBuffer(byteString.length);
        const ia = new Uint8Array(ab);
        for (let i = 0; i < byteString.length; i++) {
          ia[i] = byteString.charCodeAt(i);
        }
        const blob = new Blob([ab], { type: "application/pdf" });
        const restoredFile = new File([blob], "restored.pdf", { type: "application/pdf" });
        setFile(restoredFile);
        setFileObject(restoredFile);
        setLocalPdfUrl(URL.createObjectURL(restoredFile));
      }
      if (savedR2Key) { // Restore r2Key if found
        setR2Key(savedR2Key);
      }
    }
  }, [file, fileObject]);

  // Reset justMarked when user resets or starts a new marking session
  useEffect(() => {
    if (!markingResults) {
      setJustMarked(false);
      // Refresh credits after leaving results page (e.g., after reset or new marking)
      refreshCredits();
    }
  }, [markingResults]);

  return (
    <div className={styles.container} style={{ width: "90%", maxWidth: "90vw" }}>
      <h1>Past Papers Smart Mark</h1>
      <div className={styles.splitView}>
        <div className={styles.leftPanel}>
          {!(file || pdfUrl || localPdfUrl) ? (
            <Dropzone onFileAccepted={handleFileChange} errorMessage={dropzoneError || undefined} />
          ) : (
            <>
              <div
                style={{
                  marginBottom: "8px",
                  position: "sticky",
                  top: 0,
                  zIndex: 2,
                  background: "#fff",
                  paddingTop: 4,
                  paddingBottom: 4,
                }}
              >
                <button type="button" onClick={handleZoomOut} className={styles.button} style={{ marginRight: 4 }}>-</button>
                <button type="button" onClick={handleZoomReset} className={styles.button} style={{ marginRight: 4 }}>Reset</button>
                <button type="button" onClick={handleZoomIn} className={styles.button}>+</button>
                <span style={{ marginLeft: 12 }}>Zoom: {Math.round(scale * 100)}%</span>
              </div>
              <div style={{ background: "#fff", borderRadius: 8, border: "2px solid #222", padding: 8, marginBottom: 8, height: "70vh", overflowY: "scroll", scrollbarWidth: "thin", scrollbarColor: "#1976d2 #e0e0e0" }}>
                <Document
                  file={file}
                  onLoadSuccess={onDocumentLoadSuccess}
                  onLoadError={onDocumentLoadError}
                >
                  {numPages &&
                    Array.from(new Array(numPages), (el, index) => (
                      <Page
                        key={`page_${index + 1}`}
                        pageNumber={index + 1}
                        scale={scale}
                      />
                    ))}
                </Document>
              </div>
            </>
          )}
        </div>
        <div className={styles.rightPanel}>
          {/* Only render LockedPageGuard when not justMarked/results not shown */}
          {!justMarked && (
            <LockedPageGuard />
          )}
          {/* Timer and Input */}
          <div style={{
            display: "flex",
            alignItems: "center",
            gap: "10px",
            marginBottom: "12px",
            background: "#f5f7fa",
            borderRadius: 8,
            boxShadow: "0 2px 8px 0 rgba(60,72,88,0.07)",
            padding: "8px 12px",
            minHeight: 44,
            position: "relative",
            zIndex: 3,
            flexWrap: "nowrap", // Changed from "wrap" to "nowrap"
            justifyContent: "center"
          }}>
            <label htmlFor="testDuration" style={{ fontWeight: 600, fontSize: "1.1em", marginRight: "10px", color: '#222' }}>Set Test Duration (mins):</label> {/* Updated and combined label */}
            <input
              id="testDuration"
              type="number"
              value={testDuration}
              onChange={(e) => setTestDuration(parseInt(e.target.value) || 0)}
              min="1"
              style={{
                width: 60,
                padding: "4px 8px",
                border: "1px solid #bbb",
                borderRadius: 5,
                fontSize: "1em",
                textAlign: "center",
                color: '#222' // Set text color to dark
              }}
            />
            <Timer initialMinutes={testDuration} />
          </div>

          <div style={{
            display: "flex",
            justifyContent: "flex-end",
            alignItems: "center",
            gap: "6px",
            marginBottom: "12px",
            background: "#f5f7fa",
            borderRadius: 8,
            boxShadow: "0 2px 8px 0 rgba(60,72,88,0.07)",
            padding: "8px 6px",
            minHeight: 44,
            position: "relative",
            zIndex: 3,
            flexWrap: "nowrap"
          }}>
            {markingResults ? (
              <>
                <span style={{
                  fontWeight: 600,
                  color: "#1976d2",
                  background: "#e3f2fd",
                  borderRadius: 8,
                  padding: "3px 8px",
                  marginRight: 4,
                  fontSize: "0.98em",
                  letterSpacing: 0.3,
                  display: "inline-block",
                  minWidth: 80,
                  textAlign: "center"
                }}>
                  Attempted: {
                    answerKeys.filter((key: string) => (answers[key] && answers[key].trim() !== "")).length
                  }/{answerKeys.length}
                </span>
                <span style={{
                  fontWeight: 600,
                  color: "#43a047",
                  background: "#e8f5e9",
                  borderRadius: 8,
                  padding: "3px 8px",
                  marginRight: 4,
                  fontSize: "0.98em",
                  letterSpacing: 0.3,
                  display: "inline-block",
                  minWidth: 80,
                  textAlign: "center"
                }}>
                  Correct: {
                    answerKeys.filter((key: string) =>
                      markingResults.result &&
                      markingResults.result[key] &&
                      markingResults.result[key].correct === true
                    ).length
                  }/{answerKeys.length}
                </span>
                <button
                  type="button"
                  className={styles.button}
                  style={{
                    marginRight: 4,
                    background: "#ffe082",
                    color: "#b28704",
                    border: "1px solid #ffe082",
                    fontWeight: 600,
                    minWidth: 110,
                    fontSize: "0.97em",
                    padding: "6px 8px"
                  }}
                  onClick={handleRegenerateAllExplanations}
                  disabled={
                    answerKeys.length === 0 ||
                    Object.values(regenLoading).some(Boolean)
                  }
                >
                  {Object.values(regenLoading).some(Boolean)
                    ? "Remarking..."
                    : "Remark This Paper"}
                </button>
                <button
                  className={styles.button}
                  onClick={handleReset}
                  type="button"
                  style={{ background: "#f44336", color: "#fff", minWidth: 70, fontSize: "0.97em", padding: "6px 8px" }}
                >
                  Reset
                </button>
              </>
            ) : (
              <>
                <button
                  className={styles.button}
                  onClick={handleStartRendering}
                  disabled={loading || !fileObject}
                  type="button"
                >
                  {loading ? "Processing..." : "Render Questions"}
                </button>
                <button
                  className={styles.button}
                  onClick={handleReveal}
                  disabled={loading || deducting || credits === null || credits <= 0 || answerKeys.length === 0}
                  type="button"
                  style={{
                    background: "#43a047",
                    color: "#fff",
                    fontWeight: "bold",
                    textTransform: "uppercase",
                    letterSpacing: "1px"
                  }}
                >
                  {loading || deducting ? "Marking..." : "MARK THIS PAPER"}
                </button>
                <button
                  className={styles.button}
                  onClick={handleReset}
                  type="button"
                  style={{ background: "#f44336", color: "#fff" }}
                >
                  Reset
                </button>
              </>
            )}
          </div>
          {/* Credit error/success messages */}
          {creditError && (
            <div style={{ color: "#e53935", marginTop: 8, fontWeight: 600 }}>{creditError}</div>
          )}
          {/* {creditSuccess && (
            <div style={{ color: "#43a047", marginTop: 8, fontWeight: 600 }}>{creditSuccess}</div>
          )} */}
          {credits !== null && credits <= 0 && (
            <div style={{ color: "#b28704", marginTop: 8, fontWeight: 600 }}>
              You have 0 credits. Please purchase more to continue using locked features.
            </div>
          )}
              {!markingResults ? (
                <>
                  <h2>Answer Input Panel</h2>
                  {answerKeys.length > 0 ? (
                    <form>
                      {answerKeys.map((key: string) => (
                        <div key={key} className={styles.questionInput} style={{ marginBottom: 18 }}>
                          <div style={{ display: "flex", alignItems: "center", marginBottom: 2 }}>
                            <label
                              htmlFor={`q_${key}`}
                              style={{
                                marginRight: 8,
                                fontWeight: "bold",
                                color: "#222",
                                fontSize: 16,
                                letterSpacing: 0.5,
                              }}
                            >
                              {key}:
                            </label>
                          </div>
                          <textarea
                            id={`q_${key}`}
                            className={styles.input} // Keep existing class if it provides relevant styling
                            placeholder="Enter answer..."
                            value={answers[key] || ""}
                            onChange={(e) => handleAnswerChange(key, e.target.value)}
                            autoComplete="off"
                            rows={2} // Set to 2 lines
                            style={{
                              background: "#fff",
                              border: "1px solid #bbb",
                              borderRadius: 5,
                              padding: "8px 10px",
                              fontSize: 16,
                              marginTop: 2,
                              marginBottom: 2,
                              boxSizing: "border-box",
                              outline: "none",
                              width: "100%",
                              color: "#222",
                              fontWeight: 500,
                              letterSpacing: 0.2,
                              resize: "vertical", // Allow vertical resizing
                            }}
                          />
                        </div>
                      ))}
                    </form>
                  ) : (
                    <p style={{ color: "#222", fontSize: 15, lineHeight: 1.5, whiteSpace: "pre-line" }}>
                      1. Upload your PDF file and click "Render Questions" to generate the input boxes.
                      {"\n"}2. After the questions are completed, click "Mark This Paper" to see the answers.
                      {"\n"}3. To start over with a new PDF, click the "Reset" button.
                    </p>
                  )}
                </>
              ) : (
            <>
              <h2>Marking Results</h2>
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  gap: 8,
                  marginBottom: 12,
                  position: "sticky",
                  top: 0,
                  zIndex: 2,
                  background: "#f5f7fa",
                  paddingTop: 4,
                  paddingBottom: 4,
                }}
              >
                <span style={{ fontWeight: 500 }}>Font Size:</span>
                <button
                  type="button"
                  className={styles.button}
                  style={{ minWidth: 32, padding: "4px 10px" }}
                  onClick={() => setResultsFontSize((f) => Math.max(f - 2, 12))}
                >A-</button>
                <button
                  type="button"
                  className={styles.button}
                  style={{ minWidth: 32, padding: "4px 10px" }}
                  onClick={() => setResultsFontSize(16)}
                >Reset</button>
                <button
                  type="button"
                  className={styles.button}
                  style={{ minWidth: 32, padding: "4px 10px" }}
                  onClick={() => setResultsFontSize((f) => Math.min(f + 2, 32))}
                >A+</button>
                <span style={{ marginLeft: 8 }}>{resultsFontSize}px</span>
                {/* Removed duplicate Regenerate All Answers button from Marking Results font size row */}
              </div>
              {markingResults.error ? (
                <p className={styles.error}>{markingResults.error}</p>
              ) : (
                <div style={{ fontSize: resultsFontSize }}>
                  {answerKeys.map((key: string) => {
                    const mark = markingResults.result && markingResults.result[key];
                    const questionText = questions[key] || "";
                    const userAnswer = answers[key] || "";
                    const correctAnswer = answerSheet[key] || "";
                    return (
                      <div
                        key={key}
                        style={{
                          border: mark && mark.correct === true
                            ? "2px solid #43a047"
                            : mark && mark.correct === false
                            ? "2px solid #e53935"
                            : "2px solid #ddd",
                          borderRadius: 14,
                          padding: "18px 20px",
                          marginBottom: 24,
                          background: "#fff",
                          boxShadow: "0 2px 12px 0 rgba(60,72,88,0.07)",
                          transition: "border 0.2s, box-shadow 0.2s"
                        }}
                      >
                        <div style={{
                          display: "flex",
                          alignItems: "center",
                          marginBottom: 12,
                          gap: 10
                        }}>
                          <span style={{
                            fontWeight: 700,
                            fontSize: "1.15em",
                            color: "#1976d2",
                            letterSpacing: 1
                          }}>
                            {key}
                          </span>
                          {mark && mark.correct === true && (
                            <span
                              style={{
                                color: "#43a047",
                                fontSize: "1.5em",
                                fontWeight: "bold",
                                marginLeft: 4,
                                display: "flex",
                                alignItems: "center"
                              }}
                              aria-label="Correct"
                            >
                              <svg width="28" height="28" viewBox="0 0 24 24" fill="none"><circle cx="12" cy="12" r="12" fill="#e8f5e9"/><path d="M7 13.5l3 3 7-7" stroke="#43a047" strokeWidth="2.2" strokeLinecap="round" strokeLinejoin="round"/></svg>
                              <span style={{ marginLeft: 4, fontWeight: 600 }}>Correct</span>
                            </span>
                          )}
                          {mark && mark.correct === false && (
                            <span
                              style={{
                                color: "#e53935",
                                fontSize: "1.5em",
                                fontWeight: "bold",
                                marginLeft: 4,
                                display: "flex",
                                alignItems: "center"
                              }}
                              aria-label="Incorrect"
                            >
                              <svg width="28" height="28" viewBox="0 0 24 24" fill="none"><circle cx="12" cy="12" r="12" fill="#ffebee"/><path d="M8 8l8 8M16 8l-8 8" stroke="#e53935" strokeWidth="2.2" strokeLinecap="round"/></svg>
                              <span style={{ marginLeft: 4, fontWeight: 600 }}>Incorrect</span>
                            </span>
                          )}
                        </div>
                        <div style={{
                          display: "flex",
                          flexDirection: "row",
                          gap: 32,
                          flexWrap: "wrap",
                          marginBottom: 10
                        }}>
                          <div style={{ minWidth: 180, flex: 1 }}>
                            <div style={{ fontWeight: 500, color: "#555", marginBottom: 2 }}>Your Answer</div>
                            <div style={{
                              background: "#f1f8e9",
                              color: "#222",
                              borderRadius: 6,
                              padding: "7px 12px",
                              fontWeight: 600,
                              fontSize: "1.08em",
                              display: "flex",
                              alignItems: "center"
                            }}>
                              {userAnswer !== undefined && userAnswer !== "" ? userAnswer : <span style={{ color: "#b28704" }}>No answer provided</span>}
                              {mark && mark.correct === false && (
                                <span style={{ color: "#e53935", fontSize: "1.2em", fontWeight: "bold", marginLeft: 8 }} aria-label="Incorrect">✗</span>
                              )}
                            </div>
                          </div>
                          <div style={{ minWidth: 180, flex: 1 }}>
                            <div style={{ fontWeight: 500, color: "#555", marginBottom: 2 }}>Correct Answer</div>
                            <div style={{
                              background: "#e3f2fd",
                              color: "#1976d2",
                              borderRadius: 6,
                              padding: "7px 12px",
                              fontWeight: 600,
                              fontSize: "1.08em"
                            }}>
                              {correctAnswer !== undefined && correctAnswer !== "" ? correctAnswer : <span style={{ color: "#b28704" }}>No correct answer available</span>}
                            </div>
                          </div>
                        </div>
                        {markingResults.result?.[key]?.explanation && (
                          <div
                            style={{
                              color: "#263238",
                              background: "#fffde7",
                              borderRadius: 8,
                              padding: "14px 16px",
                              marginTop: 12,
                              fontSize: "1.05em",
                              border: "1.5px solid #ffe082",
                              boxShadow: "0 1px 4px 0 rgba(255, 235, 130, 0.08)"
                            }}
                          >
                            <div style={{ display: "flex", alignItems: "center", gap: 10, marginBottom: 6 }}>
                              <div style={{ fontWeight: 600, color: "#fbc02d", fontSize: "1.08em", display: "flex", alignItems: "center" }}>
                                <svg width="22" height="22" viewBox="0 0 24 24" fill="none" style={{ verticalAlign: "middle", marginRight: 6 }}><circle cx="12" cy="12" r="12" fill="#fffde7"/><path d="M12 7v4m0 4h.01" stroke="#fbc02d" strokeWidth="2" strokeLinecap="round"/></svg>
                                Explanation / Working
                              </div>
                              <button
                                type="button"
                                className={styles.button}
                                style={{ fontSize: "0.95em", padding: "4px 10px", background: "#ffe082", color: "#b28704", border: "1px solid #ffe082", marginLeft: 8, minWidth: 90 }}
                                onClick={() => handleRegenerateExplanation(key)}
                                disabled={regenLoading[key]}
                              >
                                {regenLoading[key] ? "Regenerating..." : "Regenerate Ans"}
                              </button>
                              <button
                                type="button"
                                className={styles.button}
                                style={{ fontSize: "0.95em", padding: "4px 10px", background: "#e3f2fd", color: "#1976d2", border: "1px solid #90caf9", marginLeft: 8, minWidth: 90 }}
                                onClick={() => setAskAIState((prev: any) => ({
                                  ...prev,
                                  [key]: {
                                    ...prev[key],
                                    show: !prev[key]?.show,
                                    input: "",
                                    loading: false,
                                    error: null,
                                    response: prev[key]?.response || null,
                                  }
                                }))}
                              >
                                ASK AI
                              </button>
                            </div>
                            <div style={{ whiteSpace: "pre-wrap", lineHeight: 1.7 }}>
                              {regenLoading[key]
                                ? <span style={{ color: "#b28704" }}>Generating new explanation...</span>
                                : markingResults.result[key].explanation}
                            </div>
                            {askAIState[key]?.show && (
                              <div style={{ marginTop: 12, background: "#f1f8e9", borderRadius: 8, padding: 12, border: "1px solid #c8e6c9" }}>
                                <form
                                  onSubmit={async (e) => {
                                    e.preventDefault();
                                    setAskAIState((prev: any) => ({
                                      ...prev,
                                      [key]: { ...prev[key], loading: true, error: null }
                                    }));
                                    try {
                                      const userQuestion = askAIState[key]?.input || "";
                                      let base64Pdf: string | undefined = undefined;
                                      if (!r2Key && typeof window !== "undefined") {
                                        base64Pdf = localStorage.getItem("pdfAutoMarkingFile") || undefined;
                                      }
                                      const payload = {
                                        r2Key,
                                        base64Pdf,
                                        questionKey: key,
                                        userAnswer,
                                        correctAnswer,
                                        questionText,
                                        explanation: markingResults.result[key].explanation,
                                        userQuestion,
                                      };
                                      const res = await fetch("/api/ai/chat", {
                                        method: "POST",
                                        headers: { "Content-Type": "application/json" },
                                        body: JSON.stringify(payload),
                                      });
                                      if (!res.ok) throw new Error("Failed to get AI response");
                                      const data = await res.json();
                                      setAskAIState((prev: any) => ({
                                        ...prev,
                                        [key]: {
                                          ...prev[key],
                                          loading: false,
                                          response: data.reply || "No response from AI.",
                                          error: null,
                                        }
                                      }));
                                    } catch (err: any) {
                                      setAskAIState((prev: any) => ({
                                        ...prev,
                                        [key]: {
                                          ...prev[key],
                                          loading: false,
                                          error: err.message || "Error contacting AI.",
                                        }
                                      }));
                                    }
                                  }}
                                >
                                  <div style={{ display: "flex", gap: 8, alignItems: "center" }}>
                                    <input
                                      type="text"
                                      className={styles.input}
                                      placeholder="Ask a question about this answer..."
                                      value={askAIState[key]?.input || ""}
                                      onChange={e =>
                                        setAskAIState((prev: any) => ({
                                          ...prev,
                                          [key]: { ...prev[key], input: e.target.value }
                                        }))
                                      }
                                      style={{
                                        flex: 1,
                                        padding: "8px 10px",
                                        border: "1px solid #bbb",
                                        borderRadius: 5,
                                        fontSize: 15,
                                        outline: "none",
                                      }}
                                      disabled={askAIState[key]?.loading}
                                    />
                                    <button
                                      type="submit"
                                      className={styles.button}
                                      style={{
                                        background: "#1976d2",
                                        color: "#fff",
                                        fontWeight: 600,
                                        minWidth: 80,
                                        fontSize: "0.98em"
                                      }}
                                      disabled={askAIState[key]?.loading || !askAIState[key]?.input}
                                    >
                                      {askAIState[key]?.loading ? "Asking..." : "Send"}
                                    </button>
                                  </div>
                                  {askAIState[key]?.error && (
                                    <div style={{ color: "#e53935", marginTop: 6 }}>
                                      {askAIState[key].error}
                                    </div>
                                  )}
                                </form>
                                {askAIState[key]?.response && (
                                  <div style={{
                                    marginTop: 10,
                                    background: "#e3f2fd",
                                    borderRadius: 6,
                                    padding: "10px 12px",
                                    color: "#1976d2",
                                    fontWeight: 500,
                                    fontSize: "1.05em"
                                  }}>
                                    <span style={{ fontWeight: 600 }}>AI:</span> {askAIState[key].response}
                                  </div>
                                )}
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
}

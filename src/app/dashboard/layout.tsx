'use client'

import { useState, useEffect } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import Link from 'next/link'
import Image from 'next/image' // Added Image import
import {
  HomeIcon,
  ChartBarIcon,
  UserCircleIcon,
  DocumentTextIcon,
  Cog6ToothIcon,
} from '@heroicons/react/24/outline'
import Header from '@/components/dashboard/Header'
import { Tooltip } from '@/components/ui/tooltip'
import { CreditsProvider } from '@/components/dashboard/CreditsContext'
import LockedPageGuard from '@/components/dashboard/LockedPageGuard'

interface NavItem {
  name: string
  href: string
  icon: typeof HomeIcon
  locked?: boolean
}

import { isPageLocked } from '@/lib/utils'

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  // Debug: Log the env variable directly in the component
  if (typeof window !== 'undefined') {
    // eslint-disable-next-line no-console
    // console.log('DASHBOARD LAYOUT LOCKED_PAGES ENV:', process.env.NEXT_PUBLIC_LOCKED_PAGES);
  }
  // Build navigation at runtime to ensure env variable is read correctly
  const navigation: NavItem[] = [
    { name: 'Overview', href: '/dashboard', icon: HomeIcon },
    // Removed 'Page 1' entry
    {
      name: 'Smart Mark',
      href: '/dashboard/smartmark',
      icon: DocumentTextIcon,
      locked: isPageLocked('/dashboard/smartmark')
    },
  ];
  const [isLoading, setIsLoading] = useState(true)
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const router = useRouter()
  const pathname = usePathname()
  const supabase = createClientComponentClient()

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('sidebarOpen')
      if (saved !== null) {
        setSidebarOpen(saved === 'true')
      } else {
        const isDesktop = window.innerWidth >= 768
        setSidebarOpen(isDesktop)
        localStorage.setItem('sidebarOpen', String(isDesktop))
      }
    }
  }, [])

  useEffect(() => {
    const checkSession = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession()
        if (!session) {
          router.replace('/auth')
          return
        }
        setIsAuthenticated(true)
      } catch (error) {
        console.error('Error checking session:', error)
        router.replace('/auth')
      } finally {
        setIsLoading(false)
      }
    }

    checkSession()

    const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, session) => {
      if (!session) {
        router.replace('/auth')
        setIsAuthenticated(false)
      } else {
        setIsAuthenticated(true)
      }
      setIsLoading(false)
    })

    return () => {
      subscription.unsubscribe()
    }
  }, [router, supabase.auth])

  if (isLoading) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-white">Loading...</div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return null
  }

  return (
    <CreditsProvider>
      <LockedPageGuard />
      <div className="min-h-screen bg-black">
        <Header />

        {/* Toggle button - visible on all screen sizes */}
        <button
          onClick={() => {
            setSidebarOpen((prev) => {
              localStorage.setItem('sidebarOpen', String(!prev))
              return !prev
            })
          }}
          className="fixed top-4 left-4 z-50 p-2 rounded-md bg-[#111111] border border-white/10 text-white hover:bg-[#222] focus:outline-none focus:ring-2 focus:ring-[#FFBE1A]"
        >
          <svg
            className="h-6 w-6"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            {sidebarOpen ? (
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            ) : (
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 6h16M4 12h16M4 18h16"
              />
            )}
          </svg>
        </button>

        <div className="flex h-[calc(100vh-4rem)] pt-16">
          {/* Sidebar and main content */}
          <div
            className={`
              fixed left-0 top-16 w-64 h-[calc(100vh-4rem)] bg-[#111111] border-r border-white/5 overflow-y-auto z-40
              transform transition-transform duration-200 ease-in-out
              ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
            `}
            style={{ position: 'fixed' }}
          >
            <nav className="p-4 space-y-1">
              {/* Branding at the top of the sidebar */}
              <div className="mb-6">
                <Link href="/" className="text-xl font-bold text-white flex items-center space-x-2">
                  <Image src="/logo.svg" alt="Logo" width={32} height={32} priority />
                  <span>TestPaperHero</span>
                </Link>
              </div>
              {navigation.map((item) => {
                const isActive = pathname === item.href
                return (
                  <Link
                    key={item.name}
                    href={item.href}
                    className={`
                      flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors
                      ${isActive
                        ? 'bg-white/10 text-white'
                        : 'text-white/60 hover:bg-white/5 hover:text-white'
                      }
                    `}
                    onClick={() => {
                      if (typeof window !== 'undefined' && window.innerWidth < 768) {
                        setSidebarOpen(false)
                        localStorage.setItem('sidebarOpen', 'false')
                      }
                    }}
                  >
                    <item.icon className="w-5 h-5 mr-3" />
                    {item.name}
                    {item.locked && (
                      <Tooltip id={`tooltip-${item.name}`} content="Subscription required">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                          className="w-4 h-4 ml-2 text-yellow-500"
                        >
                          <path fillRule="evenodd" d="M10 1a4.5 4.5 0 00-4.5 4.5V9H5a2 2 0 00-2 2v6a2 2 0 002 2h10a2 2 0 002-2v-6a2 2 0 00-2-2h-.5V5.5A4.5 4.5 0 0010 1zm3 8V5.5a3 3 0 10-6 0V9h6z" clipRule="evenodd" />
                        </svg>
                      </Tooltip>
                    )}
                  </Link>
                )
              })}

              <div className="mt-6 border-t border-white/10 pt-4 space-y-1">
                <div className="px-3 text-xs uppercase tracking-wider text-white/40 font-semibold mb-2">General</div>

                <Link
                  href="/dashboard/billing"
                  className="flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors text-white/60 hover:bg-white/5 hover:text-white"
                  onClick={() => {
                    if (typeof window !== 'undefined' && window.innerWidth < 768) {
                      setSidebarOpen(false)
                      localStorage.setItem('sidebarOpen', 'false')
                    }
                  }}
                >
                  <Cog6ToothIcon className="w-5 h-5 mr-3" />
                  Subscriptions
                </Link>

                <Link
                  href="/dashboard/profile"
                  className="flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors text-white/60 hover:bg-white/5 hover:text-white"
                  onClick={() => {
                    if (typeof window !== 'undefined' && window.innerWidth < 768) {
                      setSidebarOpen(false)
                      localStorage.setItem('sidebarOpen', 'false')
                    }
                  }}
                >
                  <UserCircleIcon className="w-5 h-5 mr-3" />
                  Profile
                </Link>

                <button
                  onClick={async () => {
                    try {
                      await supabase.auth.signOut()
                      router.replace('/auth')
                    } catch (error) {
                      // Optionally handle error
                      console.error('Error signing out:', error)
                    }
                  }}
                  className="w-full flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors text-white/60 hover:bg-white/5 hover:text-white"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5 mr-3">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 9V5.25A2.25 2.25 0 0013.5 3h-6A2.25 2.25 0 005.25 5.25v13.5A2.25 2.25 0 007.5 21h6a2.25 2.25 0 002.25-2.25V15" />
                    <path strokeLinecap="round" strokeLinejoin="round" d="M18 12H9m0 0l3-3m-3 3l3 3" />
                  </svg>
                  Sign Out
                </button>
              </div>
            </nav>
          </div>

          {/* Main Content */}
          <div
            className={`
              flex-1 overflow-y-auto p-4 sm:p-6 lg:p-8 transition-all duration-200 ease-in-out
              ${sidebarOpen ? 'md:ml-64' : ''}
            `}
          >
            {children}
          </div>
        </div>
      </div>
    </CreditsProvider>
  )
}

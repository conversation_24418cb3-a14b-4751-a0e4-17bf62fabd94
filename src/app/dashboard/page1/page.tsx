'use client'

import { useState, useEffect } from 'react'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { useCredits } from '@/components/dashboard/CreditsContext'

export default function Page1() {
  const { credits, setCredits, refreshCredits } = useCredits()
  const [loading, setLoading] = useState(true)
  const [deducting, setDeducting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const supabase = createClientComponentClient()

  // Fetch user credits on mount
  useEffect(() => {
    const fetchCredits = async () => {
      setLoading(true)
      setError(null)
      try {
        await refreshCredits()
      } catch (err) {
        setError('Unexpected error fetching credits')
      } finally {
        setLoading(false)
      }
    }
    fetchCredits()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  // Deduct 1 credit
  const handleDeduct = async () => {
    setDeducting(true)
    setError(null)
    setSuccess(null)
    try {
      const res = await fetch('/api/credits/deduct', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ amount: 1 }),
      })
      const result = await res.json()
      if (result.success) {
        setSuccess('Credit deducted!')
        await refreshCredits()
      } else {
        setError(result.error || 'Failed to deduct credit')
      }
    } catch (err) {
      setError('Unexpected error deducting credit')
    } finally {
      setDeducting(false)
    }
  }

  return (
    <div className="flex flex-col items-center justify-center min-h-[calc(100vh-64px)]">
      <h1 className="text-3xl font-bold mb-4">Page 1</h1>
      <p className="text-gray-600 mb-4">This is a protected page for subscribed users only.</p>
      <div className="mb-4">
        {loading ? (
          <span>Loading credits...</span>
        ) : (
          <span className="font-semibold">
            Credits: {credits !== null ? credits : 'N/A'}
          </span>
        )}
      </div>
      <button
        className="px-6 py-2 bg-blue-600 text-white rounded-lg font-medium disabled:opacity-50"
        onClick={handleDeduct}
        disabled={deducting || loading || credits === null || credits <= 0}
      >
        {deducting ? 'Deducting...' : 'Use 1 Credit'}
      </button>
      {error && (
        <div className="mt-4 text-red-500">{error}</div>
      )}
      {success && (
        <div className="mt-4 text-green-500">{success}</div>
      )}
      {credits !== null && credits <= 0 && (
        <div className="mt-4 text-yellow-600 font-semibold">
          You have 0 credits. Please purchase more to continue using locked features.
        </div>
      )}
    </div>
  )
}

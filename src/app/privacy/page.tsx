'use client'

import Link from "next/link";

import Header from '../components/Header'

export default function Privacy() {
  return (
    <>
      <Header />
      <main className="min-h-screen bg-[#0A0A0A] text-white px-6 pt-24 pb-12 max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold mb-6">Privacy Policy</h1>
        <p className="mb-4">
          At TestPaperHero, we are committed to protecting your privacy and ensuring the security of your personal data. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you use our services.
        </p>
        <h2 className="text-2xl font-semibold mt-8 mb-4">1. Information We Collect</h2>
        <p className="mb-4">
          We collect personal information that you voluntarily provide to us when registering, using our services, or contacting us. This may include your name, email address, phone number, and any other information you provide.
        </p>
        <h2 className="text-2xl font-semibold mt-8 mb-4">2. Use of Information</h2>
        <p className="mb-4">
          We use your information to provide, maintain, and improve our services, communicate with you, and comply with legal obligations. We do not sell or rent your personal data to third parties.
        </p>
        <h2 className="text-2xl font-semibold mt-8 mb-4">3. Data Security</h2>
        <p className="mb-4">
          We implement appropriate technical and organizational measures to protect your personal data against unauthorized access, alteration, disclosure, or destruction.
        </p>
        <h2 className="text-2xl font-semibold mt-8 mb-4">4. Data Retention and Removal</h2>
        <p className="mb-4">
          We keep your personal data and uploaded content for as long as needed for the purposes in this policy or as required by law. We also have procedures to remove content if we receive valid copyright infringement notices, as explained in our Copyright Policy.
        </p>
<h2 className="text-2xl font-semibold mt-8 mb-4">5. Legal Compliance</h2>
<p className="mb-4">
  We are committed to following all applicable laws, including copyright laws, in Singapore.
</p>
<h2 className="text-2xl font-semibold mt-8 mb-4">6. Your Rights</h2>
<p className="mb-4">
  You have the right to access, correct, or delete your personal data. You may also object to or restrict certain processing of your data. To exercise these rights, please contact <NAME_EMAIL>.
</p>
<h2 className="text-2xl font-semibold mt-8 mb-4">7. Changes to This Policy</h2>
<p className="mb-4">
  We may update this Privacy Policy from time to time. We will notify you of any changes by posting the new policy on this page.
</p>
<h2 className="text-2xl font-semibold mt-8 mb-4">8. Contact Us</h2>
<p>
  If you have any questions about this Privacy Policy, please contact <NAME_EMAIL>.
</p>
      </main>
      <footer className="py-20 px-4 border-t border-white/5 bg-[#121212]">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            <div>
              <h3 className="font-semibold text-white mb-4">Navigation</h3>
              <ul className="space-y-2">
                <li><Link href="/#home" className="text-white/70 hover:text-white">Home</Link></li>
                <li><Link href="/#why" className="text-white/70 hover:text-white">Why</Link></li>
                <li><Link href="/#how" className="text-white/70 hover:text-white">How It Works</Link></li>
                <li><Link href="/#pricing" className="text-white/70 hover:text-white">Pricing</Link></li>
                <li><Link href="/#faq" className="text-white/70 hover:text-white">FAQ</Link></li>
                <li><Link href="/#contact" className="text-white/70 hover:text-white">Contact</Link></li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold text-white mb-4">Legal</h3>
              <ul className="space-y-2">
                <li><Link href="/terms" className="text-white/70 hover:text-white">Terms & Conditions</Link></li>
                <li><Link href="/privacy" className="text-white/70 hover:text-white">Privacy</Link></li>
                <li><Link href="/#contact" className="text-white/70 hover:text-white">Contact</Link></li>
              </ul>
            </div>
          </div>
          <div className="text-center text-white/60 mt-12">
            &copy; 2025 TestPaperHero. All rights reserved.
          </div>
        </div>
      </footer>
    </>
  )
}

import { NextResponse } from 'next/server'

export async function GET(request: Request) {
  try {
    // Get the base URL from the request
    const baseUrl = new URL(request.url).origin
    
    // Call the logout API
    const response = await fetch(`${baseUrl}/api/auth/logout`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      throw new Error('Failed to log out')
    }

    // Redirect to auth page
    return NextResponse.redirect(new URL('/auth', baseUrl), {
      status: 302
    })
  } catch (error) {
    // Redirect to auth page with error message
    const baseUrl = new URL(request.url).origin
    const url = new URL('/auth', baseUrl)
    url.searchParams.set('error', 'Failed to log out. Please try again.')
    return NextResponse.redirect(url, {
      status: 302
    })
  }
}

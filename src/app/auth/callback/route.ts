import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { NextResponse } from 'next/server'

export const dynamic = 'force-dynamic'

export async function GET(request: Request) {
  const requestUrl = new URL(request.url)
  const code = requestUrl.searchParams.get('code')

  if (code) {
    const supabase = createRouteHandlerClient({ cookies })
    const { data, error } = await supabase.auth.exchangeCodeForSession(code)
    if (error) {
      console.error('Auth callback - Session exchange error:', error)
      // Redirect to auth page with error message
      const redirectUrl = new URL('/auth', requestUrl.origin)
      redirectUrl.searchParams.set('error', 'OAuth session exchange failed')
      return NextResponse.redirect(redirectUrl.toString())
    }
    // Optionally log data for debugging
    // console.log('Auth callback - Session exchange result:', { data })
  }

  // URL to redirect to after sign in process completes
  return NextResponse.redirect(requestUrl.origin + '/dashboard')
}

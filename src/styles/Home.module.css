.container {
  width: 100vw;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0;
  font-family: Arial, sans-serif;
  box-sizing: border-box;
  overflow: hidden;
}

.splitView {
  display: flex;
  gap: 0;
  margin: 0;
  width: 100%;
  max-width: 100vw;
  height: calc(100vh - 64px);
  min-height: 400px;
  box-sizing: border-box;
}

.leftPanel {
  flex: 1 1 0;
  border: 1px solid #ccc;
  padding: 16px;
  overflow-y: auto;
  height: 100%;
  min-width: 0;
  background: #fff;
  box-sizing: border-box;
}

.rightPanel {
  flex: 1 1 0;
  border: 1px solid #ccc;
  padding: 16px;
  height: 100%;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
  min-width: 320px;
  box-sizing: border-box;
  scrollbar-width: thick;
  scrollbar-color: #1976d2 #e0e0e0;
}
.rightPanel::-webkit-scrollbar {
  width: 16px;
}
.rightPanel::-webkit-scrollbar-thumb {
  background: #1976d2;
  border-radius: 8px;
  border: 3px solid #e0e0e0;
}
.rightPanel::-webkit-scrollbar-track {
  background: #e0e0e0;
  border-radius: 8px;
}

.leftPanel {
  flex: 1 1 0;
  border: 1px solid #ccc;
  padding: 16px;
  overflow-y: auto;
  height: 100%;
  min-width: 0;
  background: #fff;
  box-sizing: border-box;
  scrollbar-width: thick;
  scrollbar-color: #1976d2 #e0e0e0;
}
.leftPanel::-webkit-scrollbar {
  width: 16px;
}
.leftPanel::-webkit-scrollbar-thumb {
  background: #1976d2;
  border-radius: 8px;
  border: 3px solid #e0e0e0;
}
.leftPanel::-webkit-scrollbar-track {
  background: #e0e0e0;
  border-radius: 8px;
}

.questionInput {
  margin-bottom: 10px;
}

.questionInput label {
  display: block;
  margin-bottom: 4px;
  font-weight: bold;
}

.questionInput input {
  width: 100%;
  padding: 6px;
  font-size: 14px;
  box-sizing: border-box;
}

.button {
  margin-top: auto;
  padding: 10px 20px;
  font-size: 16px;
  cursor: pointer;
  border: none;
  border-radius: 8px;
  background: linear-gradient(90deg, #1976d2 0%, #42a5f5 100%);
  color: #fff;
  font-weight: 600;
  box-shadow: 0 2px 8px 0 rgba(25, 118, 210, 0.08);
  transition: background 0.2s, box-shadow 0.2s, transform 0.1s;
  outline: none;
}

.button:hover:not(:disabled),
.button:focus:not(:disabled) {
  background: linear-gradient(90deg, #1565c0 0%, #1e88e5 100%);
  box-shadow: 0 4px 16px 0 rgba(25, 118, 210, 0.16);
  transform: translateY(-2px) scale(1.03);
}

.button:active:not(:disabled) {
  background: linear-gradient(90deg, #1976d2 0%, #42a5f5 100%);
  box-shadow: 0 1px 4px 0 rgba(25, 118, 210, 0.10);
  transform: translateY(1px) scale(0.98);
}

.button:disabled {
  background: #e0e0e0;
  color: #aaa;
  cursor: not-allowed;
  box-shadow: none;
}

.results {
  margin-top: 20px;
  white-space: pre-wrap;
  background-color: #f9f9f9;
  padding: 10px;
  border-radius: 4px;
  font-family: monospace;
  font-size: 14px;
}

.error {
  color: red;
  font-weight: bold;
}

.input {
  width: 100%;
  padding: 8px 10px;
  font-size: 16px;
  border: 1px solid #bbb;
  border-radius: 5px;
  box-sizing: border-box;
  outline: none;
  transition: border-color 0.2s;
}
.input:focus {
  border-color: #1976d2;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .splitView {
    flex-direction: column;
    height: auto;
    min-height: auto;
  }

  .leftPanel,
  .rightPanel {
    width: 100%;
    flex: none; /* Remove flex grow/shrink on mobile */
    min-width: auto; /* Allow panels to shrink below 320px on small mobile screens */
    height: auto; /* Allow content to dictate height */
  }

  .leftPanel {
    margin-bottom: 16px; /* Add some space between stacked panels */
  }
}

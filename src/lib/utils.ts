import { ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function isPageLocked(currentPath: string): boolean {
  // if (typeof window !== 'undefined') {
  //   // Log the value in the browser console for debugging
  //   // eslint-disable-next-line no-console
  //   // console.log('LOCKED_PAGES ENV:', process.env.NEXT_PUBLIC_LOCKED_PAGES);
  // }
  const lockedPages = process.env.NEXT_PUBLIC_LOCKED_PAGES?.split(',') || [];
  return lockedPages.includes(currentPath);
}

export function absoluteUrl(path: string) {
  return `${process.env.NEXT_PUBLIC_APP_URL}${path}`
}

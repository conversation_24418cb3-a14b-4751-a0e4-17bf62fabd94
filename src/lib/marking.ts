import { GoogleGenerativeAI } from "@google/generative-ai";

const GEMINI_API_KEY = process.env.GEMINI_API_KEY || "YOUR_GEMINI_API_KEY_HERE";

export async function markSingleQuestion({
  base64Pdf,
  questionKey,
  questionText,
  correctAnswer,
  userAnswer,
}: {
  base64Pdf: string;
  questionKey: string;
  questionText: string;
  correctAnswer: string;
  userAnswer: string;
}): Promise<{ correct: boolean | null; explanation: string; raw: string }> {
  const prompt = `
You are an exam marker. You are given:
- The full PDF of the exam paper, including all images, charts, and figures.
- The question key: ${questionKey}
- The full question text: "${questionText}"
- The correct answer: "${correctAnswer}"
- The user's answer: "${userAnswer}"

IMPORTANT:
- You MUST reference the question text, the correct answer, and the PDF (including any images, charts, or figures) in your explanation.
- You MUST analyze the PDF and describe any relevant images, charts, or figures that help solve the question.
- If you do not reference the PDF, question, and correct answer for every explanation, your response will be rejected.
- If the user's answer is blank, provide a full, step-by-step worked solution as if teaching a student from scratch, referencing the question, correct answer, and PDF.

Your job:
- Compare the user's answer to the correct answer for this question.
- Mark as correct if the user's answer is contextually similar in meaning to the correct answer, even if the wording is different. For example, "Mary caught a fish and cooked it." and "Mary cooked the fish she caught." should both be marked correct. Only mark as incorrect if the user's answer is factually wrong or misses key information.
- Do NOT invent, guess, or use any information not present in the correct answer, question text, or the PDF.
- Output:
  - "correct: yes" or "correct: no"
  - "explanation:"
    - ALWAYS provide a detailed, step-by-step explanation, whether the answer is correct, incorrect, or unanswered.
    - Write your explanation in simple, easy-to-understand English, using point form or numbered steps.
    - Do NOT start with "The correct answer is..." or "The user's answer is...". Go straight to the reasoning and working.
    - Be concise and to the point. Use simple terms and avoid unnecessary detail.
    - The explanation must teach the user how to arrive at the correct answer, including:
      - A breakdown of the reasoning, calculations, or logic needed to solve the question, referencing the question text AND the PDF (including any images, charts, or figures).
      - Show all working steps, formulas, or logic in a clear, instructional manner (use bullet points or numbered steps).
      - If the question is conceptual, explain the concept and why the user's answer is incorrect or missing.
      - If the question is multi-step, walk through each step.
      - The goal is to help the user learn how to solve the question, not just state the answer.
      - If the answer is correct, still provide a full worked solution and explanation.
      - You MUST analyze the PDF for this question, and reference any relevant images, charts, or figures in your explanation.
- Format your output as a clear, user-friendly markdown block, using this format:

**question [${questionKey}]:**
**correct:** yes/no
**explanation:**
[Your explanation in simple, point form English, starting directly with the reasoning/working]
`;

  const genAI = new GoogleGenerativeAI(GEMINI_API_KEY);
  const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash" });

  const contents = [
    {
      role: "user",
      parts: [
        { text: prompt },
        {
          inlineData: {
            mimeType: "application/pdf",
            data: base64Pdf,
          },
        },
      ],
    },
  ];

  const response = await model.generateContent({ contents });
  const text =
    response?.response?.candidates?.[0]?.content?.parts?.[0]?.text ||
    response?.response?.candidates?.[0]?.content?.parts?.map((p: any) => p.text).join("\n") ||
    "";

  // Parse markdown-style output
  const correctMatch = text.match(/\*\*correct:\*\*\s*(yes|no)/i);
  const correct = correctMatch ? /yes/i.test(correctMatch[1]) : null;
  const explanationMatch = text.match(/\*\*explanation:\*\*\s*([\s\S]*?)(?=\*\*question\s+[A-Za-z0-9]+:\*\*|$)/i);
  let explanation = explanationMatch ? explanationMatch[1].trim() : "";
  if (!explanation) explanation = "No explanation provided.";

  return {
    correct,
    explanation,
    raw: text,
  };
}

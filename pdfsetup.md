# PDF Auto-Marking Feature Setup Checklist

This document outlines the steps required to implement the paid PDF auto-marking feature using Gemini AI.

**Phase 1: Setup & Backend Development**

1.  **[ ] Environment & Dependencies:**
    *   [ ] Obtain a Google Gemini API key.
    *   [ ] Add `GEMINI_API_KEY=YOUR_API_KEY` to `.env.local`.
    *   [ ] Add `GEMINI_API_KEY=` to `.env.Example`.
    *   [ ] Install necessary npm packages:
        *   `npm install @google/generative-ai`
        *   `npm install pdf-parse` (or alternative PDF parsing library)
        *   `npm install react-pdf pdfjs-dist` (or alternative PDF viewing library)

2.  **[ ] API Route - PDF Processing (`/api/pdf/process`):**
    *   [ ] Create API route file: `src/app/api/pdf/process/route.ts`.
    *   [ ] Implement file upload logic (e.g., using `request.formData()`). Consider temporary storage or Supabase Storage.
    *   [ ] Implement PDF text extraction using the chosen parsing library.
    *   [ ] **Develop robust logic to identify and parse the answer sheet section within the PDF text.** (Define assumptions or required format).
    *   [ ] Extract the number of questions and attempt to infer types (MCQ, open-ended).
    *   [ ] Implement access control: Check user authentication and subscription/credit status via Supabase data. Deny access if unauthorized.
    *   [ ] Return structured JSON (e.g., `{ numberOfQuestions: number, questionTypes: string[], answerKey: any }`) or an error response.

3.  **[ ] API Route - AI Marking (`/api/pdf/mark`):**
    *   [ ] Create API route file: `src/app/api/pdf/mark/route.ts`.
    *   [ ] Define request body structure (user answers, answer key data).
    *   [ ] Implement access control: Check user authentication and subscription/credit status.
    *   [ ] Initialize Google AI SDK (`@google/generative-ai`) with the API key.
    *   [ ] **Craft a detailed prompt for Gemini Flash:** Include context, answer key, user answers, instructions for comparison, feedback generation (only for wrong answers), and desired JSON output format.
    *   [ ] Make the API call to Gemini (`generateContent`).
    *   [ ] Parse the AI's JSON response. Handle potential errors from the AI model.
    *   [ ] If using credits: Implement logic to decrement user credits in Supabase database. Ensure atomicity if possible.
    *   [ ] Return structured JSON marking results (e.g., `[{ question: number, correct: boolean, feedback?: string }, ...]`) or an error response.

**Phase 2: Frontend Development**

4.  **[ ] New Dashboard Page (`/dashboard/pdf-marker`):**
    *   [ ] Create page file: `src/app/dashboard/pdf-marker/page.tsx`.
    *   [ ] Add a link/navigation item to this page in the dashboard layout/header.
    *   [ ] Implement page-level access control: Fetch user subscription/credit status on load. Show upgrade prompt or redirect if access is denied.

5.  **[ ] UI Component - PDF Uploader:**
    *   [ ] Create a new React component (e.g., `src/components/pdf/PdfUploader.tsx`).
    *   [ ] Add `<input type="file" accept=".pdf">`.
    *   [ ] Handle file selection state.
    *   [ ] Add a button to trigger the API call to `/api/pdf/process`.
    *   [ ] Implement loading state display (e.g., spinner).
    *   [ ] Implement error message display (e.g., using toasts).

6.  **[ ] UI Component - PDF Viewer:**
    *   [ ] Create a new React component (e.g., `src/components/pdf/PdfViewer.tsx`).
    *   [ ] Integrate `react-pdf` (`Document`, `Page` components).
    *   [ ] Pass the uploaded file (or URL) as a prop.
    *   [ ] Handle PDF loading and rendering. Include basic controls if needed (zoom, pagination).

7.  **[ ] UI Component - Dynamic Input Area:**
    *   [ ] Create a new React component (e.g., `src/components/pdf/InputArea.tsx`).
    *   [ ] Receive `numberOfQuestions` and `questionTypes` as props.
    *   [ ] Dynamically render input fields (`.map()`). Use appropriate input types.
    *   [ ] Manage the state of user answers (e.g., an array or object).
    *   [ ] Provide clear numbering for each input field corresponding to the questions.

8.  **[ ] UI Component - Results Display:**
    *   [ ] Create a new React component (e.g., `src/components/pdf/ResultsDisplay.tsx`).
    *   [ ] Receive the marking results array as a prop.
    *   [ ] Render each result: question number, correctness indicator (icon/text), and feedback for incorrect answers.
    *   [ ] Style appropriately (e.g., green for correct, red for incorrect).

9.  **[ ] Page Assembly & Layout (`pdf-marker/page.tsx`):**
    *   [ ] Implement the main page component logic.
    *   [ ] Manage overall page state (idle, uploading, processing, readyForInput, marking, resultsReady).
    *   [ ] Conditionally render the `PdfUploader` or the main view (Viewer + InputArea).
    *   [ ] Implement the side-by-side layout (e.g., using Flexbox or Grid).
    *   [ ] Add the "Submit for Marking" button.
    *   [ ] Implement the `onClick` handler for the submit button: gather inputs, call `/api/pdf/mark`, handle loading/errors, update state with results.
    *   [ ] Conditionally render the `ResultsDisplay` component.

**Phase 3: Integration & Refinement**

10. **[ ] Payment/Subscription Integration:**
    *   [ ] Verify access control logic in API routes (`/api/pdf/process`, `/api/pdf/mark`).
    *   [ ] Verify access control logic on the frontend page (`/dashboard/pdf-marker`).
    *   [ ] Ensure users without access are clearly informed and directed towards upgrading (link to `/dashboard/billing` or pricing page).
    *   [ ] If using credits, ensure the decrement logic is reliable and handles potential race conditions if applicable.

11. **[ ] Error Handling & User Experience:**
    *   [ ] Review and enhance error handling across the entire flow.
    *   [ ] Provide informative messages for common issues (invalid PDF, parsing failure, AI error, insufficient credits).
    *   [ ] Ensure loading states are clear and prevent double submissions.
    *   [ ] Test responsiveness of the side-by-side layout.

12. **[ ] Styling:**
    *   [ ] Apply Tailwind CSS classes consistently to all new components.
    *   [ ] Ensure the new feature visually integrates with the existing dashboard design.

**Phase 4: Testing & Documentation**

13. **[ ] Testing:**
    *   [ ] Write backend tests for API routes (unit/integration).
    *   [ ] Test PDF parsing with diverse PDF examples (different structures, lengths, answer sheet formats).
    *   [ ] Test AI marking prompts and validate feedback quality.
    *   [ ] Write frontend tests for components and page interactions (e.g., using React Testing Library or Cypress).
    *   [ ] Manually test the end-to-end flow with various user accounts (subscribed, non-subscribed, with/without credits).
    *   [ ] Test edge cases (large files, empty PDFs, network interruptions).

14. **[ ] Documentation:**
    *   [ ] Ensure this `pdfsetup.md` file is complete and checked into version control.
    *   [ ] Add comments to complex code sections (especially PDF parsing and AI prompting).
    *   [ ] Consider adding user-facing documentation or tooltips if needed.

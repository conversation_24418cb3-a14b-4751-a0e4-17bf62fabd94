# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url      # Your Supabase project URL (public, required)
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key      # Your Supabase anon/public key (public, required)
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Application Configuration
NEXT_PUBLIC_APP_URL=your_app_url              # The base URL of your app (e.g., http://localhost:3000)

# Stripe Configuration
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret

# Stripe Price IDs with Credit Values
NEXT_PUBLIC_STRIPE_PRICE_STARTER=your_starter_price_id
NEXT_PUBLIC_STRIPE_PRICE_STARTER_QUANTITY=15
NEXT_PUBLIC_STRIPE_PRICE_PRO=your_pro_price_id
NEXT_PUBLIC_STRIPE_PRICE_PRO_QUANTITY=60
NEXT_PUBLIC_STRIPE_PRICE_ENTERPRISE=your_enterprise_price_id
NEXT_PUBLIC_STRIPE_PRICE_ENTERPRISE_QUANTITY=300

# Feature/Page Access Control
NEXT_PUBLIC_LOCKED_PAGES=/dashboard/page1,/dashboard/smartmark

# AI Features (Optional)
OPENAI_API_KEY=your_openai_api_key

# Resend Configuration
RESEND_API_KEY=your_resend_api_key

# Credit System Configuration
NEXT_PUBLIC_INITIAL_USER_CREDITS=5

# Toggle credits visibility on dashboard (true = show, false = hide)
NEXT_PUBLIC_SHOW_CREDITS=true

# Gemini API Key (replace with your actual key)
GEMINI_API_KEY=your_gemini_api_key

# Cloudflare R2 Credentials (replace with your actual credentials)
R2_ACCESS_KEY_ID=your_r2_access_key_id
R2_SECRET_ACCESS_KEY=your_r2_secret_access_key
R2_BUCKET=your_r2_bucket_name
R2_ENDPOINT=your_r2_endpoint
NEXT_PUBLIC_R2_ENDPOINT=your_public_r2_endpoint

# Umami Analytics
NEXT_PUBLIC_UMAMI_WEBSITE_ID=your_umami_website_id

# Posthog Analytics
NEXT_PUBLIC_POSTHOG_API_KEY=your_posthog_id
NEXT_PUBLIC_POSTHOG_API_HOST=https://us.i.posthog.com

# Google Tag
NEXT_PUBLIC_GOOGLE_TAG_ID=your_google_tag_id
# Credit System Implementation Plan

This plan outlines the steps to integrate a credit system into the Next.js SaaS Kit, leveraging Supabase and Stripe.

**Assumptions:**

*   Initial Credits (Configurable via `env`): Default 10 (placeholder)
*   Credits per Plan (Configurable via `env`): Starter: 50, Pro: 100, Enterprise: 200
*   Credit Consumption: 1 credit per use of a designated "locked feature".

**Checklist:**

1.  **Environment Variable Setup:**
    *   [ ] Add the following variables to `.env.local` (create if it doesn't exist) and `.env.Example`:
        ```dotenv
        # Credit System Configuration
        NEXT_PUBLIC_INITIAL_USER_CREDITS=10 # Default credits for new users
        CREDITS_PRICE_STARTER=50            # Credits granted for Starter plan
        CREDITS_PRICE_PRO=100               # Credits granted for Pro plan
        CREDITS_PRICE_ENTERPRISE=200        # Credits granted for Enterprise plan
        ```
    *   *Note:* `NEXT_PUBLIC_INITIAL_USER_CREDITS` is public if needed on the client, others are server-side. Adjust prefix if needed.

2.  **Database Schema Modification (Supabase):**
    *   [ ] Add an integer column named `credits` to the `public.profiles` table.
        *   Set a default value (e.g., 0 or match `NEXT_PUBLIC_INITIAL_USER_CREDITS`).
        *   Ensure it cannot be negative (add a CHECK constraint `credits >= 0`).
    *   *SQL Migration Example (`supabase/migrations/YYYYMMDDHHMMSS_add_credits_column.sql`):*
        ```sql
        -- Add credits column to profiles table
        ALTER TABLE public.profiles
        ADD COLUMN credits INT NOT NULL DEFAULT 0; -- Or use env var default if possible during setup

        -- Add constraint to ensure credits are not negative
        ALTER TABLE public.profiles
        ADD CONSTRAINT check_credits_non_negative CHECK (credits >= 0);
        ```
    *   [ ] Apply the migration using Supabase CLI or dashboard.

3.  **Grant Initial Credits on User Signup:**
    *   [ ] **Option A (Recommended: Supabase Function/Trigger):**
        *   Create a Supabase database function (e.g., `handle_new_user`) triggered after a new user is inserted into `auth.users`.
        *   This function should insert a corresponding row into `public.profiles` and set the initial `credits` value by reading `process.env.NEXT_PUBLIC_INITIAL_USER_CREDITS` (requires function environment variable setup in Supabase).
        *   *Alternative within function:* If reading env vars in trigger is complex, set the default in the migration (Step 2) and ensure the profile insert happens automatically. *Review existing user creation logic.*
    *   [ ] **Option B (Modify Existing Logic):**
        *   If user profile creation is handled elsewhere (e.g., Next.js API route after signup), modify that logic to include the initial `credits` value read from `process.env.NEXT_PUBLIC_INITIAL_USER_CREDITS`.

4.  **Update Stripe Webhook Handler:**
    *   [ ] Locate the Stripe webhook handler: `src/app/api/webhooks/stripe/route.ts`.
    *   [ ] Modify the handler to listen for relevant events indicating a successful subscription payment (e.g., `invoice.paid`, potentially `checkout.session.completed` for the *first* payment).
    *   [ ] Inside the event handler:
        *   Extract the Stripe `price_id` and `customer_id` (or `user_id` if mapped).
        *   Determine the credit amount based on the `price_id` by mapping it to the environment variables (`CREDITS_PRICE_STARTER`, `CREDITS_PRICE_PRO`, `CREDITS_PRICE_ENTERPRISE`).
        *   Fetch the corresponding Supabase `user_id` using the `customer_id`.
        *   Use the Supabase service role client to **set** the `credits` column in the `public.profiles` table for that user to the determined `creditAmount`.
        *   *Example (Conceptual Supabase update):*
            ```typescript
            // Inside webhook handler after identifying user and credit amount
            const { error } = await supabaseAdmin
              .from('profiles')
              .update({ credits: creditAmount }) // Set credits to the plan amount
              .eq('id', userId);

            if (error) {
              console.error('Error setting user credits:', error);
              // Handle error appropriately
            }
            ```

5.  **Implement Credit Deduction Logic:**
    *   [ ] **Create Backend Function (API Route or Supabase Function):**
        *   Create a new API route (e.g., `src/app/api/credits/deduct/route.ts`) or a Supabase Edge Function.
        *   This function should be protected (require authentication).
        *   It should accept the amount to deduct (default to 1).
        *   Inside the function:
            *   Get the authenticated `user_id`.
            *   Attempt to decrement the `credits` in `public.profiles` atomically, ensuring `credits >= amount_to_deduct`.
            *   Return `{ success: true }` if decrement was successful.
            *   Return `{ success: false, error: 'Insufficient credits' }` if credits were too low.
            *   *Example (Conceptual Supabase RPC function `decrement_user_credits`):*
                ```sql
                -- Supabase SQL function for atomic decrement
                CREATE OR REPLACE FUNCTION decrement_user_credits(user_id_to_update uuid, credits_to_deduct int)
                RETURNS boolean AS $$
                DECLARE
                  updated_rows int;
                BEGIN
                  UPDATE public.profiles
                  SET credits = credits - credits_to_deduct
                  WHERE id = user_id_to_update AND credits >= credits_to_deduct;

                  GET DIAGNOSTICS updated_rows = ROW_COUNT;
                  RETURN updated_rows > 0;
                END;
                $$ LANGUAGE plpgsql SECURITY DEFINER; -- Use SECURITY DEFINER carefully
                ```
    *   [ ] **Modify Frontend Feature Components:**
        *   Identify the components representing the "locked features".
        *   Before executing the feature's action:
            *   Fetch the user's current credit balance (can be part of user profile data fetched on load).
            *   If credits > 0:
                *   Call the backend deduction function/API route (e.g., `POST /api/credits/deduct`).
                *   If the deduction call returns `success: true`, proceed with the feature's action.
                *   If the deduction call returns `success: false`, show an "Insufficient Credits" message/modal.
            *   If credits <= 0 initially, disable the feature button/action and show an "Insufficient Credits" message/modal.

6.  **Display Credit Balance in UI:**
    *   [ ] Fetch the `credits` value along with other profile data when the user logs in or navigates to the dashboard.
    *   [ ] Display the `credits` value in relevant UI locations (e.g., `src/components/dashboard/Header.tsx`, `src/app/dashboard/profile/page.tsx`).

7.  **Update Supabase Types:**
    *   [ ] Regenerate Supabase types to include the new `credits` column in the `profiles` type definition. Run the appropriate command (check `package.json` scripts, often `npm run update-types` or similar).
    *   [ ] Update `src/types/supabase.ts` if manual changes are needed or verify the auto-generated types.

8.  **Testing:**
    *   [ ] Test user signup: Verify initial credits are granted.
    *   [ ] Test Stripe subscription: Simulate a subscription purchase/renewal and verify credits are added correctly via the webhook.
    *   [ ] Test feature usage: Verify credits are deducted correctly.
    *   [ ] Test insufficient credits: Verify feature access is blocked.

# SaaS Kit Pro – Complete Setup & Onboarding Guide

## Project Purpose & Overview

**SaaS Kit Pro** is a modern SaaS starter kit designed for automarking PDF past exam papers. It enables users (e.g., students or educators) to upload PDF exam papers, have them automatically marked using AI or custom logic, and receive instant feedback—all within a credit-based subscription system.

**What This Project Does:**
- Users upload PDF exam papers via the dashboard.
- The system extracts questions and answers from the PDF.
- AI or custom logic automatically marks the answers.
- Marking results and feedback are returned to the user.
- Each marking operation deducts credits from the user's balance.
- Users can purchase more credits or subscribe to plans for continued access.

**Key Features:**
- Next.js 15 App Router & Server Actions
- Supabase authentication & Postgres database
- Stripe subscription billing & credit system
- Resend transactional email integration
- Tailwind CSS & shadcn/ui for beautiful, responsive UI
- PDF upload, extraction, and automarking workflow
- Credit-based page access & auto-redirects
- Profile management, billing, and dashboard
- SEO optimized, dark mode, and type-safe

---

## Project Directory Structure

```
.
├── src/
│   ├── app/                        # Next.js app router, pages, API routes
│   │   ├── api/
│   │   │   ├── extract-questions/  # API for extracting questions from PDF
│   │   │   ├── mark/               # API for automarking logic
│   │   │   ├── credits/            # API for credit deduction
│   │   │   └── webhooks/stripe/    # Stripe webhook handler
│   ├── components/                 # Shared React components (auth, dashboard, UI)
│   ├── lib/                        # Utility functions (e.g., isPageLocked, marking, email)
│   ├── styles/                     # Global styles (CSS)
│   ├── types/                      # TypeScript type definitions
│   └── utils/                      # Stripe/Supabase helpers
├── public/                         # Static assets (images, SVGs, avatars)
├── supabase/
│   └── migrations/                 # SQL migrations (schema, triggers, jobs)
├── .env.Example                    # Example environment variables
├── package.json                    # Project manifest, scripts, dependencies
├── README.md                       # Project overview (high-level)
├── SETUP.md                        # This detailed setup guide
└── ...config files                 # ESLint, Tailwind, TypeScript configs, etc.
```

**Key Files & Folders:**
- `src/app/api/extract-questions/route.ts` – Extracts questions/answers from uploaded PDFs
- `src/app/api/mark/route.ts` – Automarking logic (AI or custom)
- `src/app/api/credits/deduct/route.ts` – Deducts credits per marking
- `src/components/dashboard/LockedPageGuard.tsx` – Credit-based page access guard
- `src/lib/marking.ts` – Marking logic helpers
- `supabase/migrations/20250415134300_selected_tables_schema.sql` – Main DB schema, triggers, jobs

---

## Technical Architecture

### System Workflow
The application follows a multi-step workflow for PDF automarking:

1. **PDF Upload & Extraction**
   - Users upload PDF via dashboard
   - File is processed by `/api/extract-questions/route.ts` which:
     - Uses Gemini API for PDF analysis
     - Extracts questions, answers, and associated media
     - Returns structured JSON with question keys and content
   - Alternative R2 storage integration for PDF handling

2. **Automarking Logic**
   - Extracted data sent to `/api/mark/route.ts`
   - AI marking powered by Google's Gemini API (requires `GEMINI_API_KEY`)
   - Custom prompt engineering for:
     - Contextual answer comparison
     - Detailed explanation generation
     - Media-aware marking (charts, diagrams, etc.)
   - Fallback mechanism for AI unavailability

3. **Credit Management System**
   - Atomic credit deduction via `/api/credits/deduct/route.ts`
   - Database triggers for subscription-based credit updates
   - Monthly credit reset for unsubscribed users via cron job
   - Stripe webhook integration for plan changes and refunds

### Data Flow Diagram
```mermaid
graph TD
    A[User Uploads PDF] --> B[Extract Questions API]
    B --> C[Store Extracted Data]
    C --> D[Marking API]
    D --> E[Generate Results]
    E --> F[Update Credits]
    F --> G[Database Triggers]
    G --> H[Stripe Webhooks]
    H --> I[Subscription Management]
```

### Database Architecture
Key tables and relationships:
- **profiles**: User data + credit balance
- **customer_subscriptions**: Stripe subscription mapping
- **exam_attempts**: Marking history
- **questions**: Extracted questions storage
- **credit_transactions**: Audit log

Triggers:
- `update_credits_on_subscription` - Updates credits on subscription changes
- `monthly_credit_reset` - Scheduled job for unsubscribed users

### 4. Results Delivery
- Marking results and feedback are returned to the user in the dashboard.
- Results may include per-question marks, total score, and AI-generated explanations.

### 5. Subscription & Billing
- Users can subscribe to plans (Starter, Pro, Enterprise) via Stripe Checkout.
- Each plan grants a set number of credits per month.
- Stripe webhooks update subscription status and credits in the database.
- Refunds/disputes trigger automatic credit reset and subscription cancellation.

### 6. Email Notifications
- Users receive transactional emails (welcome, billing, etc.) via Resend.

---

## Prerequisites

- **Node.js** 18.17 or higher
- **npm** (comes with Node.js)
- **Git**
- Accounts for:
  - [Supabase](https://supabase.com)
  - [Stripe](https://stripe.com)
  - [Resend](https://resend.com) (for email)
  - [Google Gemini](https://makersuite.google.com) (for AI marking)

---

## Step-by-Step Setup Instructions

### 1. Clone the Repository

```bash
git clone <repository-url>
cd <repository-name>
```

### 2. Install Dependencies

```bash
npm install
```

### 3. Set Up Environment Variables

- Copy the example file:
  ```bash
  cp .env.Example .env.local
  ```
- Open `.env.local` and fill in all required values (see [Environment Variables Reference](#environment-variables-reference) below).

### 4. Supabase Setup

#### a. Create a Supabase Project
- Sign up at [supabase.com](https://supabase.com) and create a new project.
- In the Supabase dashboard, go to **Project Settings > API** to get your project URL and anon key.
- Go to **Project Settings > Database** to get your service_role key.

#### b. Apply Database Schema & Migrations
- Install the [Supabase CLI](https://supabase.com/docs/guides/cli).
- Authenticate the CLI and link to your project.
- Apply migrations:
  ```bash
  supabase db push
  ```
  This will apply all SQL in `supabase/migrations/`.

#### c. Set Up Scheduled Cron Job
- The schema includes a monthly cron job to reset credits for unsubscribed users.
- To create it manually, run:
  ```sql
  SELECT cron.schedule(
    'reset-unsubscribed-credits-job',
    '0 0 1 * *',
    $$SELECT reset_unsubscribed_credits();$$
  );
  ```
- The function and job are defined in the migration SQL.

### 5. Stripe Setup

#### a. Create a Stripe Account
- Sign up at [stripe.com](https://stripe.com).
- Get your API keys from the Stripe Dashboard.

#### b. Set Up Products & Prices
- Create products and prices for each plan (Starter, Pro, Enterprise).
- Copy the Price IDs into your `.env.local` (see below).

#### c. Set Up Webhook Endpoint
- In the Stripe Dashboard, add a webhook endpoint:
  ```
  https://<your-domain>/api/webhooks/stripe
  ```
- Enable the following events:
  - `checkout.session.completed`
  - `customer.subscription.updated`
  - `customer.subscription.deleted`
  - `charge.refunded`
  - `charge.dispute.created`
  - `charge.dispute.funds_withdrawn`

#### d. Test Webhooks Locally
- Install the [Stripe CLI](https://stripe.com/docs/stripe-cli).
- Forward events to your local server:
  ```bash
  stripe listen --forward-to localhost:3000/api/webhooks/stripe
  ```

### 6. Resend (Email) Setup

- Sign up at [resend.com](https://resend.com).
- Get your API key and add it to `.env.local` as `RESEND_API_KEY`.

### 7. Google Gemini (AI Marking)

- Sign up at [makersuite.google.com](https://makersuite.google.com).
- Get your API key and add it to `.env.local` as `GEMINI_API_KEY`.
- If not set, the system will use fallback or custom logic for marking.

---

## Environment Variables Reference

All variables below are required unless marked optional.

| Variable                                 | Description                                                                                 | Public?   |
|-------------------------------------------|---------------------------------------------------------------------------------------------|-----------|
| NEXT_PUBLIC_SUPABASE_URL                  | Your Supabase project URL                                                                   | Yes       |
| NEXT_PUBLIC_SUPABASE_ANON_KEY             | Supabase anon/public key                                                                    | Yes       |
| SUPABASE_SERVICE_ROLE_KEY                 | Supabase service_role key (keep secret)                                                     | No        |
| POSTGRES_URL                             | Supabase Postgres connection string (optional, for CLI/tools)                               | No        |
| NEXT_PUBLIC_APP_URL                       | The base URL of your app (e.g., http://localhost:3000)                                      | Yes       |
| STRIPE_SECRET_KEY                         | Stripe secret key                                                                           | No        |
| STRIPE_WEBHOOK_SECRET                     | Stripe webhook signing secret                                                               | No        |
| NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY        | Stripe publishable key                                                                      | Yes       |
| NEXT_PUBLIC_STRIPE_PRICE_STARTER          | Stripe Price ID for Starter plan                                                            | Yes       |
| NEXT_PUBLIC_STRIPE_PRICE_STARTER_QUANTITY | Credits for Starter plan                                                                    | Yes       |
| NEXT_PUBLIC_STRIPE_PRICE_PRO              | Stripe Price ID for Pro plan                                                                | Yes       |
| NEXT_PUBLIC_STRIPE_PRICE_PRO_QUANTITY     | Credits for Pro plan                                                                        | Yes       |
| NEXT_PUBLIC_STRIPE_PRICE_ENTERPRISE       | Stripe Price ID for Enterprise plan                                                         | Yes       |
| NEXT_PUBLIC_STRIPE_PRICE_ENTERPRISE_QUANTITY | Credits for Enterprise plan                                                              | Yes       |
| NEXT_PUBLIC_LOCKED_PAGES                  | Comma-separated list of locked dashboard pages (e.g., /dashboard/page1,/dashboard/smartmark)| Yes       |
| GEMINI_API_KEY                            | Google Gemini API key (for AI features/automarking)                                         | No        |
| RESEND_API_KEY                            | Resend API key for email sending                                                            | No        |
| NEXT_PUBLIC_INITIAL_USER_CREDITS          | Initial credits for new users                                                               | Yes       |
| NEXT_PUBLIC_SHOW_CREDITS                  | true = show credits, false = hide credits on dashboard                                      | Yes       |

**Notes:**
- All `NEXT_PUBLIC_` variables are exposed to the frontend.
- Never commit `.env.local` to version control.
- `POSTGRES_URL` is only needed for CLI/database tools, not for the app itself.
- `GEMINI_API_KEY` is required for AI features

---

## Running the Development Server

```bash
npm run dev
```
- The app will be available at [http://localhost:3000](http://localhost:3000).
- To stop the server, press `Ctrl+C`.

---

## Available npm Scripts

- `npm run dev` – Start the Next.js development server
- `npm run build` – Build the app for production
- `npm run start` – Start the production server
- `npm run lint` – Run ESLint for code quality

**Note:**  
No test scripts are included by default. Add your own tests as needed.

---

## Deployment

- Deploy easily to [Vercel](https://vercel.com/) or any platform supporting Next.js 15.
- See [Next.js deployment docs](https://nextjs.org/docs/deployment) for details.
- Set all environment variables in your deployment platform’s dashboard.

---

## Database Reset & Dependency Updates

- **Reset Database:**  
  Use the Supabase dashboard or CLI to reset your database if needed.  
  _Warning: This will delete all data!_

- **Update Dependencies:**  
  ```bash
  npm update
  ```

---

## Troubleshooting & Common Pitfalls

- **Missing Environment Variables:**  
  Double-check `.env.local` for typos or missing values.

- **Supabase Connection Issues:**  
  Ensure your keys are correct and your project is running.

- **Stripe Webhook Not Firing:**  
  - Check your webhook endpoint URL and enabled events.
  - Use the Stripe CLI to test locally.

- **Emails Not Sending:**  
  - Verify your Resend API key.
  - Check spam/junk folders.

- **CORS or 404 Errors:**  
  - Ensure API routes are correct and server is running.

- **Credits Not Updating:**  
  - Check Stripe webhook logs and Supabase triggers.

- **PDF Not Marking:**  
  - Ensure the PDF is in a supported format.
  - Check logs for errors in `/api/extract-questions` or `/api/mark`.
  - Verify your `GEMINI_API_KEY` is set and valid.

---

## Contributing

We welcome contributions!  
- Fork the repository
- Create a feature branch (`git checkout -b feature/YourFeature`)
- Commit and push your changes
- Open a Pull Request

See the [README.md](README.md) for more details.

---

## Security & Best Practices

- Never commit secrets or `.env.local` to version control.
- Use secret managers for production deployments.
- Regularly update dependencies for security patches.

---

## Support

For issues or questions, open an issue in this repository or contact the maintainer.

---

**Built with ❤️ by [Zain UI Abedeen](https://github.com/zainulabedeen123) and contributors.**

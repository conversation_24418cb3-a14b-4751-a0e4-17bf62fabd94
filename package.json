{"name": "best-saas-kit-pro", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@aws-sdk/client-s3": "^3.787.0", "@aws-sdk/s3-request-presigner": "^3.787.0", "@google/genai": "^0.9.0", "@google/generative-ai": "^0.24.0", "@heroicons/react": "2.0.18", "@react-email/components": "^0.0.36", "@react-email/render": "^1.0.3", "@stripe/stripe-js": "^5.2.0", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/supabase-js": "^2.47.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.468.0", "next": "^15.1.0", "openai": "^4.76.1", "pdfjs-dist": "4.8.69", "react": "18.2.0", "react-dom": "18.2.0", "react-email": "^4.0.7", "react-pdf": "^9.2.1", "recharts": "^2.12.0", "resend": "^4.0.1", "stripe": "^17.4.0", "tailwind-merge": "^2.5.5"}, "devDependencies": {"@types/node": "20.10.0", "@types/react": "18.2.39", "@types/react-dom": "18.2.17", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "autoprefixer": "10.4.16", "eslint": "^8.57.0", "eslint-config-next": "^15.1.0", "postcss": "8.4.31", "tailwindcss": "3.3.5", "typescript": "5.3.2"}}
-- Schema dump for selected tables: customer_subscriptions, customers, profiles, plan_credits

CREATE TABLE public.customer_subscriptions (
    id uuid NOT NULL,
    user_id uuid NOT NULL,
    subscription_id text NOT NULL,
    status text NOT NULL,
    price_id text NOT NULL,
    quantity integer NOT NULL,
    cancel_at timestamp with time zone,
    created timestamp with time zone NOT NULL,
    ended_at timestamp with time zone,
    trial_start timestamp with time zone,
    trial_end timestamp with time zone,
    stripe_portal_url text
);

CREATE TABLE public.customers (
    id uuid NOT NULL,
    user_id uuid NOT NULL,
    stripe_customer_id text NOT NULL,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL
);

CREATE TABLE public.plan_credits (
    price_id text NOT NULL,
    credits integer NOT NULL
);

CREATE TABLE public.profiles (
    id uuid NOT NULL,
    full_name text,
    username text,
    website text,
    avatar_url text,
    bio text,
    updated_at timestamp with time zone,
    credits integer NOT NULL
);

-- Functions

CREATE OR REPLACE FUNCTION public.handle_new_user()
<PERSON><PERSON><PERSON><PERSON> trigger
LANGUAGE plpgsql
AS $$
DECLARE
  default_credits integer;
BEGIN
  SELECT credits INTO default_credits FROM public.plan_credits WHERE price_id = 'default';

  INSERT INTO public.profiles (
    id,
    full_name,
    avatar_url,
    updated_at,
    credits
  )
  VALUES (
    new.id,
    COALESCE(
      new.raw_app_meta_data->>'full_name',
      new.raw_app_meta_data->>'name',
      new.raw_user_meta_data->>'full_name',
      new.raw_user_meta_data->>'name'
    ),
    COALESCE(
      new.raw_app_meta_data->>'avatar_url',
      new.raw_user_meta_data->>'avatar_url'
    ),
    now(),
    COALESCE(default_credits, 0)
  );
  RETURN new;
END;
$$;

CREATE OR REPLACE FUNCTION public.decrement_user_credits()
RETURNS boolean
LANGUAGE plpgsql
AS $$
DECLARE
  updated_rows int;
BEGIN
  UPDATE public.profiles
  SET credits = credits - credits_to_deduct
  WHERE id = user_id_to_update AND credits >= credits_to_deduct;

  GET DIAGNOSTICS updated_rows = ROW_COUNT;
  RETURN updated_rows > 0;
END;
$$;

CREATE OR REPLACE FUNCTION public.update_user_credits_after_subscription()
RETURNS trigger
LANGUAGE plpgsql
AS $$
BEGIN
  -- For active subscriptions, update credits based on price_id
  IF NEW.status = 'active' THEN
    UPDATE public.profiles
    SET credits = (
      SELECT credits
      FROM public.plan_credits
      WHERE price_id = NEW.price_id
    )
    WHERE id = NEW.user_id;
  -- For non-active subscriptions, set to default credits
  ELSE
    UPDATE public.profiles
    SET credits = (
      SELECT credits
      FROM public.plan_credits
      WHERE price_id = 'default'
    )
    WHERE id = NEW.user_id;
  END IF;

  RETURN NEW;
END;
$$;

CREATE OR REPLACE FUNCTION public.handle_subscription_change()
RETURNS trigger
LANGUAGE plpgsql
AS $$
BEGIN
  -- For new or updated active subscriptions
  IF (TG_OP = 'INSERT' OR (TG_OP = 'UPDATE' AND OLD.status != NEW.status)) AND NEW.status = 'active' THEN
    -- Update credits based on the subscription's price_id
    UPDATE public.profiles
    SET credits = (
      SELECT credits
      FROM public.plan_credits
      WHERE price_id = NEW.price_id
    )
    WHERE id = NEW.user_id;
  -- For cancelled or deleted subscriptions
  ELSIF (TG_OP = 'UPDATE' AND NEW.status = 'canceled') OR TG_OP = 'DELETE' THEN
    -- Reset to default credits
    UPDATE public.profiles
    SET credits = (
      SELECT credits
      FROM public.plan_credits
      WHERE price_id = 'default'
    )
    WHERE id = COALESCE(NEW.user_id, OLD.user_id);
  END IF;

  RETURN NEW;
END;
$$;

CREATE OR REPLACE FUNCTION public.reset_unsubscribed_credits()
RETURNS void
LANGUAGE plpgsql
AS $$
begin
  update profiles
  set credits = (select credits from plan_credits where price_id = 'default')
  where id not in (
    select user_id from customer_subscriptions where status = 'active'
  );
end;
$$;

-- Triggers

CREATE TRIGGER trigger_update_credits_after_subscription_insert
AFTER INSERT ON public.customer_subscriptions
FOR EACH ROW EXECUTE FUNCTION update_user_credits_after_subscription();

CREATE TRIGGER trigger_update_credits_after_subscription_update
AFTER UPDATE ON public.customer_subscriptions
FOR EACH ROW WHEN ((old.* IS DISTINCT FROM new.*))
EXECUTE FUNCTION update_user_credits_after_subscription();

CREATE TRIGGER handle_subscription_change
AFTER INSERT OR DELETE OR UPDATE ON public.customer_subscriptions
FOR EACH ROW EXECUTE FUNCTION handle_subscription_change();
